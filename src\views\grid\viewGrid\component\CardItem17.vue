<template>
  <!-- 横向柱状图 -->
  <div class="box">
    <div class="box-header">
      {{ title }}
      <!-- <span class="box-header-num">54</span> -->
    </div>
    <template v-if="list.length !== 0">
      <vsoc-chart
        :echartId="echartId"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { num, numberToFormat } from '@/util/filters'
import { deepClone } from '@/util/utils'
import biyadi from '../images/biyadi.svg'
import qirui from '../images/qirui.svg'
import car11 from '../images/car11.svg'
import {
  axisLabel,
  getRoundSize,
  legend,
  splitLine,
  stackSeriesFn,
  tooltip,
} from './chart'
export default {
  name: 'CardItem17',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    echartId: {
      type: String,
      default: '',
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {
      icons: [qirui, biyadi, car11],
    }
  },
  computed: {
    option() {
      let allData = deepClone(this.list).sort(
        (a, b) => a.riskScore - b.riskScore,
      )
      let xList = allData.map(v => v.riskScore)
      let yList = allData.map(v => v.platformName)
      let percents = allData.map(v => v.riskPercentage)
      let _this = this
      return {
        backgroundColor: 'transparent',
        tooltip: {
          ...tooltip(),
          trigger: 'axis',
          axisPointer: { type: 'none' },
          borderWidth: 0,
          formatter: function (value) {
            let percent = percents[value[0].dataIndex]
            return `${value[0].marker} ${value[0].name}：${num(
              value[0].data,
            )} (${percent}%)`
          },
        },
        legend: {
          ...legend(),
          show: false,
        },
        grid: {
          left: '2%',
          bottom: '6%',
          containLabel: true,
          right: '17%',
          top: _this.echartId === 'C023' ? '12%' : '8%',
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: false,
          },
          axisLabel: {
            ...axisLabel(),
            show: false,
            color: this.$route.query.isLightTheme === 'true' ? '#333' : '#fff',
            opacity: 0.7,
            formatter: function (value) {
              return numberToFormat(value)
            },
          },
        },
        yAxis: {
          type: 'category',
          boundaryGap: false,
          data: yList,
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: this.$route.query.isLightTheme === 'true' ? '#333' : '#fff',
            opacity: 0.7,
            // ...axisLabel(),
            formatter: function (params, index) {
              if (_this.echartId === 'C023') {
                return `{icon|}   {text|${params}}`
              } else {
                return `{text|${params}}`
              }
            },
            rich: {
              icon: {
                backgroundColor: {
                  image: _this.icons[2],
                },
                height: getRoundSize(17),
              },
              text: {
                color:
                  this.$route.query.isLightTheme === 'true' ? '#333' : '#fff',
                opacity: 0.7,
                fontSize: getRoundSize(12),
                padding: [4, 0, 0, 0],
              },
            },
          },
          axisPointer: {
            show: true,
            label: {
              formatter: value => {
                // conxsole.log('formatter value:', value)
                // return `${value.value} ${value.seriesData[0].data}`
              },
            },
          },
        },
        series: [
          {
            ...stackSeriesFn('#44e2fe', xList, ''),
            label: {
              show: true,
              position: 'right',
              formatter: function (params) {
                let percent = percents[params.dataIndex]
                return `{text|${num(params.value)}} {percent|(${percent}%)}`
              },
              rich: {
                text: {
                  color:
                    this.$route.query.isLightTheme === 'true' ? '#333' : '#fff',
                  opacity: 0.8,
                  fontSize: getRoundSize(10),
                },
                percent: {
                  color:
                    this.$route.query.isLightTheme === 'true' ? '#333' : '#fff',
                  opacity: 0.8,
                  fontSize: getRoundSize(10),
                },
              },
            },
          },
        ],
      }
    },
  },
}
</script>
