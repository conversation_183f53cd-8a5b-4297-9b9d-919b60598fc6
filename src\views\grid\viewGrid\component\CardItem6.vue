<template>
  <!-- 条形图2 -->
  <div class="box pb-4">
    <div class="box-header">
      {{ title }}
      <!-- <span class="box-header-num">{{ total | numberToFormat }}</span> -->
    </div>

    <div class="tab-content" style="margin-top: 1%">
      <div class="d-flex flex-column h-100">
        <template v-if="list1.length !== 0">
          <div
            v-for="(earning, index) in list1"
            :key="index"
            style="height: 25%"
            class="d-flex flex-column justify-space-around"
          >
            <div class="d-flex align-center justify-space-between">
              <v-tooltip bottom content-class="tooltip-box">
                <template v-slot:activator="{ on, attrs }">
                  <span class="right-chart1-text" v-bind="attrs" v-on="on">{{
                    earning.name
                  }}</span>
                </template>
                <div class="fs-14-1 d-flex align-center">
                  <span
                    class="tooltip-dot"
                    :style="{
                      background: alertLevel[earning.type]
                        ? alertLevel[earning.type].color
                        : '',
                    }"
                  ></span>
                  <span>{{ earning.name }}</span>
                </div>
              </v-tooltip>
              <v-progress-linear
                background-color="transparent"
                :color="
                  alertLevel[earning.type] ? alertLevel[earning.type].color : ''
                "
                :value="earning.percentage"
                class="progress-linear"
              >
              </v-progress-linear>
              <span class="right-chart1-value">
                {{ earning.value | numberToFormat }}({{ earning.percentage }})
              </span>
            </div>
          </div>
        </template>
        <template v-else>
          <div
            class="fs-16 color--primary h-100 d-flex justify-center align-center"
          >
            None
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CardItem6',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    total: {
      type: [Number, String],
      default: 0,
    },
  },
  created() {},
  data() {
    return {
      list1: this.list.map((v, index) => {
        return {
          ...v,
          type: index,
        }
      }),
    }
  },
  computed: {
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
  },
}
</script>
<style lang="scss">
.tooltip-box {
  background: rgba(255, 255, 255, 0.1) !important;
  box-shadow: rgba(0, 0, 0, 0.2) 1px 2px 10px;
  border-radius: 4px;
  color: rgb(255, 255, 255);
  font: 6px / 10px 'Microsoft YaHei';
  padding: 10px;
  backdrop-filter: blur(6px);
  pointer-events: none;
}
.tooltip-dot {
  display: inline-block;
  margin-right: 8px;
  border-radius: 10px;
  width: 10px;
  height: 10px;
}
</style>
