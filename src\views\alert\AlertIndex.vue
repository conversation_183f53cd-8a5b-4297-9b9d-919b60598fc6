<template>
  <div>
    <bread-crumb
      ref="topHeader"
      clearType="initQuery"
      :showDoQuery="showDoQuery"
      :filterList="filterList"
      :showAdvance="true"
    >
      <!-- <template slot="center">
        <div ref="tab">
          <v-tabs
            v-model="query.assetType"
            height="54"
            centered
            @change="$_search"
          >
            <v-tab
              class="mr-14"
              v-for="item in assetTypeEnum"
              :key="item.value"
              :tab-value="item.value"
              :disabled="tableLoading"
            >
              <span class="ml-2">{{ item.text }}</span>
            </v-tab>
          </v-tabs>
        </div>
      </template> -->
    </bread-crumb>

    <v-card tile class="pa-0" elevation="0">
      <v-card-text class="main-content">
        <div v-if="!updateShow" class="mb-3 d-flex justify-space-between">
          <div class="d-flex">
            <v-menu
              v-if="
                $route.meta.buttonInfo['alert-allstatus'] ||
                $route.meta.buttonInfo['alert-export']
              "
              offset-y
              left
              nudge-bottom="4"
              transition="scale-transition"
              content-class="menu-list"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  elevation="0"
                  color="primary"
                  v-bind="attrs"
                  v-on="on"
                  class="d-flex align-center"
                >
                  <span class="mr-3"> {{ $t('action.batchOps') }} </span>
                  <v-icon size="12px">mdi-chevron-down</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-has:alert-allstatus
                  class="list-hover"
                  @click="changeType(1)"
                >
                  <v-list-item-title>
                    {{ $t('alert.btn.status') }}
                  </v-list-item-title>
                </v-list-item>
                <v-list-item
                  v-has:alert-export
                  class="list-hover"
                  @click="changeType(2)"
                >
                  <v-list-item-title>
                    {{ $t('alert.btn.export1') }}
                  </v-list-item-title>
                </v-list-item>
                <v-list-item
                  v-has:alert-export
                  class="list-hover"
                  @click="exportTicket"
                >
                  <v-list-item-title>
                    {{ $t('alert.btn.export2') }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>

          <div class="d-flex align-end">
            <vsoc-date-range
              v-model="dateRange.range"
              no-title
              :menu-props="dateRange.menuProps"
              @input="onChangeDate"
            >
              <template v-slot:text="{ on, attrs }">
                <v-text-field
                  type="button"
                  clearable
                  outlined
                  dense
                  class="append-icon-max me-3 date-width"
                  readonly
                  hide-details
                  color="primary"
                  large
                  :label="$t('alert.headers.triggered')"
                  prepend-inner-icon="mdi-calendar-range-outline"
                  :value="RANGE_STR(query.startDate, query.endDate)"
                  @click:clear="onChangeDate({ start: '', end: '' })"
                ></v-text-field>
              </template>
            </vsoc-date-range>

            <v-autocomplete
              v-model="query.vehicleCompanyCode"
              :items="modelItems"
              dense
              :label="$t('alert.headers.model')"
              hide-details
              class="me-3 text-width"
              clearable
              outlined
              :menu-props="{ offsetY: true }"
              @change="$_search"
            >
            </v-autocomplete>

            <v-text-field
              v-model="query.vehicleId"
              color="primary"
              class="me-3 text-width"
              outlined
              dense
              clearable
              hide-details
              :label="$t('alert.headers.assertId')"
              @keyup.enter.native="$_search"
              @click:clear="onClear"
            ></v-text-field>

            <v-btn class="primary--text bg-btn" elevation="0" @click="$_search">
              <span>{{ $t('action.search') }}</span>
            </v-btn>
          </div>
        </div>
        <div v-else>
          <div
            v-if="type === 1"
            class="d-flex align-center justify-space-between px-2 mb-3"
          >
            <div class="d-flex align-center">
              <v-btn class="primary--text bg-btn" elevation="0" @click="reback">
                <span> {{ $t('action.reback') }} </span>
              </v-btn>
              <div class="text-ml ml-3" style="color: #1f2533">
                {{ $t('alert.btn.status') }} / {{ $t('global.selected')
                }}<span class="text-2xl mx-1" style="color: #000">{{
                  selectedArray.length
                }}</span>
              </div>
            </div>
            <div class="d-flex align-center">
              <v-select
                v-model="updateQuery.alarmLevel"
                :menu-props="{ offsetY: true, maxHeight: 600 }"
                hide-details
                :items="alertLevelList"
                :label="$t('alert.headers.severity')"
                outlined
                dense
                style="width: 180px"
                class="me-3"
                clearable
              >
                <template v-slot:item="{ item }">
                  <div class="d-flex align-center text-center">
                    <vsoc-icon
                      type="fill"
                      icon="icon-gaojingjibiebiaozhi"
                      size="14px"
                      :style="{ color: item.color }"
                      class="mr-2"
                    ></vsoc-icon>
                    <span class="text-base color-base">
                      {{ item.text }}
                    </span>
                  </div>
                </template>
              </v-select>

              <v-autocomplete
                v-model="updateQuery.warnClassifyId"
                :menu-props="{ offsetY: true, maxHeight: 400 }"
                hide-details
                :items="listAlert"
                item-value="id"
                item-text="name"
                item-disabled="disabled"
                :label="$t('alert.headers.type')"
                outlined
                dense
                style="width: 180px"
                class="me-3"
                clearable
              >
              </v-autocomplete>

              <v-select
                v-model="updateQuery.status"
                :menu-props="{ offsetY: true, maxHeight: 600 }"
                hide-details
                :items="alertStatus"
                :label="$t('alert.headers.status')"
                outlined
                dense
                style="width: 180px"
                class="me-3"
                clearable
              >
                <template v-slot:item="{ item }">
                  <span
                    class="status-color ml-0 mr-2"
                    :style="{ background: alertStatus[item.value].color }"
                  ></span>
                  <span>
                    {{ item.text }}
                  </span>
                </template>
              </v-select>

              <v-btn
                color="primary"
                elevation="0"
                :disabled="selectedArray.length === 0"
                @click="$_changeStatus"
              >
                <span> {{ $t('action.confirm') }} </span>
              </v-btn>
            </div>
          </div>

          <div v-if="type === 2" class="d-flex align-center px-2 mb-3">
            <v-btn
              elevation="0"
              color="primary"
              :disabled="selectedArray.length === 0"
              @click="exportAlert"
            >
              <span> {{ $t('action.export') }} </span>
            </v-btn>

            <v-btn
              class="primary--text bg-btn ml-3"
              elevation="0"
              @click="reback"
            >
              <span> {{ $t('action.reback') }} </span>
            </v-btn>

            <div class="text-ml ml-3" style="color: #1f2533">
              {{ $t('alert.btn.export1') }} / {{ $t('global.selected')
              }}<span class="text-2xl mx-1" style="color: #06309b">{{
                selectedArray.length
              }}</span>
            </div>
          </div>
        </div>

        <v-data-table
          ref="alearList"
          fixed-header
          :items-per-page="query.pageSize"
          :show-select="updateShow"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="flex-1 thead-light"
          checkbox-color="primary"
          :sort-by="['level']"
          :loading="tableLoading"
          @item-selected="$_tableSelected"
          @toggle-select-all="$_tableSelected"
          @click:row="clickTableRow"
        >
          <template v-slot:item.alarmLevel="{ item }">
            <div
              v-if="item.alarmLevel && alertLevel[Number(item.alarmLevel)]"
              class="d-flex align-center"
            >
              <vsoc-icon
                type="fill"
                icon="icon-gaojingjibiebiaozhi"
                size="14px"
                :style="{ color: alertLevel[Number(item.alarmLevel)].color }"
                class="mr-2"
              ></vsoc-icon>
              <span>{{ alertLevel[Number(item.alarmLevel)].text }}</span>
            </div>
            <span v-else>N/A</span>
          </template>

          <!-- <template v-slot:item.name="{ item }">
            <span v-show-tips class="font-weight-semibold">{{
              item.name
            }}</span>
          </template> -->

          <template v-slot:item.description="{ item }">
            <div v-show-tips style="width: 300px">
              {{ item.description | dataFilter }}
            </div>
          </template>

          <!-- <template v-slot:item.vehicleVin="{ item }">
            <div v-if="item.vehicleVin" class="d-flex align-center">
              <div v-show-tips style="max-width: 160px" class="text-no-wrap">
                {{ item.vehicleVin }}
              </div>
              <v-btn
                v-copy="item.vehicleVin"
                v-show-tips="$t('action.copy')"
                icon
                class="ml-1"
                @click.stop
              >
                <vsoc-icon
                  type="fill"
                  icon="icon-fuzhi"
                  class="secondary--text"
                ></vsoc-icon>
              </v-btn>
            </div>
            <div v-else>N/A</div>
          </template> -->

          <template v-slot:item.warnClassifyName="{ item }">
            <span v-show-tips class="font-weight-semibold">{{
              item.warnClassifyName | dataFilter
            }}</span>
          </template>

          <template v-slot:item.status="{ item }">
            <div v-if="alertStatus[item.status]">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="alertStatus[item.status].color"
                class="mr-1"
              ></v-badge>
              <span>{{ alertStatus[item.status].text }}</span>
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.updateUser="{ item }">
            <span>{{ item.updateUser | dataFilter }}</span>
          </template>

          <template v-slot:item.vehicleId="{ item }">
            <div v-if="item.vehicleId" class="d-flex align-center">
              <vsoc-icon
                v-if="item.assetType"
                :icon="assetTypeEnum[item.assetType].icon"
                type="fill"
                class="primary--text mr-2"
              ></vsoc-icon>
              <span v-show-tips>{{ item.vehicleId }}</span>
              <!-- <div @click.stop>
                <v-btn
                  v-copy="item.vehicleId"
                  v-show-tips="$t('action.copy')"
                  icon
                >
                  <vsoc-icon
                    type="fill"
                    icon="icon-fuzhi"
                    class="secondary--text"
                  ></vsoc-icon>
                </v-btn>
              </div> -->
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.vehicleCompanyName="{ item }">
            <span>{{ item.vehicleCompanyName | dataFilter }}</span>
          </template>
          <template v-slot:item.vehiclePlatformName="{ item }">
            <span>{{ item.vehiclePlatformName | dataFilter }}</span>
          </template>
          <template v-slot:item.alarmDate="{ item }">
            <span>{{ item.alarmDate | toDate }}</span>
          </template>
          <template v-slot:item.updateDate="{ item }">
            <span>{{ item.updateDate | toDate }}</span>
          </template>
        </v-data-table>

        <!-- 分页器 -->
        <vsoc-pagination
          :page.sync="query.pageNum"
          :size.sync="query.pageSize"
          :total="tableDataTotal"
          @change-page="getTableData"
          @change-size="$_search"
        >
        </vsoc-pagination>
      </v-card-text>
    </v-card>

    <vsoc-dialog
      v-model="showUpload"
      dense
      width="480"
      :title="$t('action.export')"
      @click:confirm="$_confirmEdit"
    >
      <v-form ref="form" v-model="valid" class="pa-4">
        <vsoc-date-range
          ref="refRange"
          v-model="dateRange1.range"
          no-title
          :menu-props="dateRange1.menuProps"
          @input="onChangeDate1"
        >
          <template v-slot:text="{ on, attrs }">
            <v-text-field
              clearable
              dense
              class="me-3 pa-0"
              readonly
              color="primary"
              large
              :label="$t('ticket.hint.time')"
              append-icon="mdi-calendar-range-outline"
              v-bind="attrs"
              v-on="on"
              :value="RANGE_STR(exportQuery.startDate, exportQuery.endDate)"
              @click:clear="onChangeDate1({ start: '', end: '' })"
              :rules="[
                v =>
                  !!v || $t('validation.required', [$t('ticket.headers.time')]),
              ]"
            ></v-text-field>
          </template>
        </vsoc-date-range>
      </v-form>
    </vsoc-dialog>

    <alert-drawer
      ref="alertsDrawer"
      v-model="showAdvanceSearch"
      :alert-tags="alertTags"
      :alert-types="alertTypes"
      :alert-type-map="alertTypeMap"
      :modelItems="modelItems"
      :platFormData="platFormData"
      @do-query="doQuery"
    ></alert-drawer>
  </div>
</template>

<script>
import { exportAlarm, getAlerts, updateStatus } from '@/api/alert'
import { getAllClassify } from '@/api/classify/index'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'

import BreadCrumb from '@/components/bread-crumb/index.vue'
import VsocDialog from '@/components/VsocDialog.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import { alertStatusList, assetTypeEnum } from '@/util/enum'
import { dataFilter } from '@/util/filters'
import {
  clearFilterItem,
  deepClone,
  handleFilterItem,
  handleQueryParams,
  setRemainingHeight,
  updateURL,
} from '@/util/utils'
import { differenceInDays, format, subDays } from 'date-fns'
import AlertDrawer from './AlertDrawer.vue'

export default {
  name: 'AlertIndex',
  components: {
    VsocPagination,
    VsocDateRange,
    AlertDrawer,
    BreadCrumb,
    VsocDialog,
  },
  filters: {
    dataFilter,
  },
  data() {
    return {
      type: '',
      valid: true,
      showUpload: false,
      exportQuery: {
        startDate: '',
        endDate: '',
      },
      dateRange1: {
        range: {
          start: '',
          end: '',
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },
      updateShow: false,
      showAdvanceSearch: false,
      // 查询条件列表
      searchConditions: [
        {
          text: '开始时间',
          value: 'startDate',
        },
        {
          text: '结束时间',
          value: 'endDate',
        },
        {
          text: '告警状态',
          value: 'status',
        },
      ],
      dateRange: {
        range: {
          start: '',
          end: '',
        },
        menuProps: { offsetY: true, closeOnContentClick: false },
      },

      // 查询条件下拉选择
      query: {
        startDate: '',
        endDate: '',
        alarmLevelList: [],
        statusList: [],
        warnClassifyIds: [],
        vehicleId: '',
        alarmTypeList: [],
        alarmId: '',
        vehicleCompanyCode: '',
        vehiclePlatformCode: '',
        pageNum: 1,
        pageSize: 10,
      },
      updateQuery: {
        alarmLevel: '',
        warnClassifyId: '',
        status: '',
      },
      // 更新状态值
      changeSelectStatus: '',
      filterList: [],

      tableData: [],
      tableLoading: false,
      tableHeight: '34.5rem',
      tableDataTotal: 0,

      // 已选择的列表
      selectedArray: [],

      // 最近位置组件所需数据
      mapInfo: {},

      // 更新状态列表
      alertStatusList,

      alertTags: [],
      listAlert: [],
      tagTypeMap: {},
      // alertTypeMap: {},
      // alertTypes: [],
      modelItems: [],
      groupNameListEnum: {},
      platFormData: [],
      platFormMap: {},
    }
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('alert.headers.severity'),
          value: 'alarmLevel',
          width: '120px',
        },
        {
          text: this.$t('alert.headers.id'),
          value: 'id',
          width: '120px',
        },
        {
          text: this.$t('alert.headers.type'),
          value: 'warnClassifyName',
          width: '200px',
        },
        {
          text: this.$t('alert.headers.description'),
          value: 'description',
          width: '300px',
        },
        {
          text: this.$t('alert.headers.asset'),
          value: 'vehicleId',
          width: '180px',
        },
        {
          text: this.$t('alert.headers.status'),
          value: 'status',
          width: '120px',
        },
        {
          text: this.$t('alert.headers.model'),
          value: 'vehicleCompanyName',
          width: '130px',
        },
        // {
        //   text: '所属车企平台',
        //   value: 'vehiclePlatformName',
        //   width: '130px',
        // },

        // {
        //   text: this.$t('alert.headers.tag'),
        //   value: 'warnClassifyName',
        //   width: '200px',
        // },
        {
          text: this.$t('alert.headers.triggered'),
          value: 'alarmDate',
          width: '180px',
        },

        {
          text: this.$t('alert.headers.update'),
          value: 'updateDate',
          width: '180px',
        },
        {
          text: this.$t('global.updateUser'),
          value: 'updateUser',
          width: '140px',
        },
      ]
    },

    alertStatus() {
      return this.$store.getters['enums/getAlertStatus']
    },
    alertStatusEum() {
      return this.$store.state.enums.enums.AlarmStatus
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    // 获取告警等级颜色
    alertLevelList() {
      return Object.assign([], this.$store.state.enums.enums.AlarmLevel)
    },

    //获取告警类型
    alertTypes() {
      return this.$store.getters['enums/getAlertType']
    },

    alertTypeMap() {
      return this.$store.getters['enums/getAlertTypeObj']
    },
  },
  created() {
    //获取告警分类
    this.getAllAlertTags()
    //获取所有车企
    this.loadModelData()
    //获取车企平台
    this.getPlatFormList()

    this.init()
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    onClear() {
      this.query.vehicleId = ''
      this.$_search()
    },
    init() {
      //查询route是否有参数
      if (
        JSON.stringify(this.$route.query) !== '{}' &&
        this.$route.query.isQuery
      ) {
        const keyList = Object.keys(this.$route.query).filter(
          v => v !== 'isQuery',
        )
        keyList.forEach(key => {
          if (Array.isArray(this.query[key])) {
            this.query[key] = JSON.parse(this.$route.query[key])
          } else {
            if (key === 'startDate') {
              this.dateRange.range.start = this.$route.query[key]
            }
            if (key === 'endDate') {
              this.dateRange.range.end = this.$route.query[key]
            }
            this.query[key] = this.$route.query[key]
          }
        })
        this.$_search()
      } else {
        const query = handleQueryParams({ type: 'get', key: 'alert' })
        if (query) {
          this.query = query
        }
        let params = { ...this.query }
        let isNotNull = Object.keys(params).find(
          key =>
            (Array.isArray(params[key]) && params[key].length) ||
            (typeof params[key] === 'string' && params[key]),
        )
        //查询是否都为空
        if (isNotNull) {
          this.$_search('pageNum')
        } else {
          this.initQuery()
        }
      }
    },

    //获取车企
    async loadModelData() {
      try {
        const data = await this.$store.dispatch('global/loadAllAutomaker', {
          is_supervise: '0',
        })
        this.modelItems = data
        this.$_appendFilterListItem()
      } catch (e) {
        console.error(`获取车企错误：${e}`)
      }
    },
    //获取车企平台
    async getPlatFormList() {
      try {
        const data = await this.$store.dispatch('global/loadPlatFormData', {
          is_supervise: '0',
        })
        this.platFormData = data
        const obj = {}
        this.platFormData.forEach(item => {
          obj[item.value] = {
            text: item.text,
          }
        })
        this.platFormMap = obj
        this.$_appendFilterListItem()
      } catch (e) {
        console.error(`获取车企平台错误：${e}`)
      }
    },
    //获取告警分类
    async getAllAlertTags() {
      try {
        const data = await this.$store.dispatch('global/loadClassifyList', {
          type: '0',
        })
        this.listAlert =
          data.map(v => {
            return {
              ...v,
              disabled: v.status === '1' ? true : false,
            }
          }) || []
        this.alertTags = data || []
        const obj = {}
        this.alertTags.forEach(item => {
          obj[item.id] = {
            text: item.name,
          }
        })
        this.tagTypeMap = obj
        this.$_appendFilterListItem()
      } catch (e) {
        console.error(`获取告警分类错误：${e}`)
      }
    },
    changeType(val) {
      this.type = val
      this.updateShow = true
    },

    //导出
    exportAlert() {
      let selectedArray = deepClone(this.selectedArray)
      const params = {
        startDate: '',
        endDate: '',
        idList: selectedArray.map(v => v.id),
      }
      this.getReport(params, () => {
        this.reback()
      })
    },

    // 导出时间改变
    onChangeDate1(range) {
      const diffDays = differenceInDays(
        new Date(range.end),
        new Date(range.start),
      )
      if (diffDays > 60) {
        return this.$notify.info('error', this.$t('alert.hint.tip'))
      }
      this.dateRange1.range = range
      this.exportQuery.startDate = range.start
      this.exportQuery.endDate = range.end
    },

    //导出
    exportTicket() {
      this.showUpload = true
      this.onChangeDate1({ start: '', end: '' })
      this.$nextTick(() => {
        this.$refs['refRange'].isPresetActive = -1
        this.$refs.form.resetValidation()
      })
    },

    //全部导出
    $_confirmEdit(callBack) {
      if (!this.$refs.form.validate()) return callBack(false, true)
      const params = {
        startDate: this.exportQuery.startDate,
        endDate: this.exportQuery.endDate,
        idList: [],
      }
      this.getReport(params, callBack)
    },
    getReport(params, callBack) {
      exportAlarm(params).then(res => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute(
          'download',
          params.startDate + '~' + params.endDate + '告警导出表.xls',
        )
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        callBack && callBack()
      })
    },
    onReset() {
      Object.assign(this.$data.query, this.$options.data.call(this).query)
      this.$_search()
    },

    RANGE_STR,

    //重置参数
    initQuery(params) {
      this.query.alarmLevelList = ['0', '1', '2', '3', '4']
      this.query.statusList = ['0', '1']
      this.handleTime()
    },

    // 处理时间
    handleTime() {
      const defalutTime = [subDays(new Date(), 7), new Date()]
      const start = format(defalutTime[0], 'yyyy-MM-dd')
      const end = format(defalutTime[1], 'yyyy-MM-dd')
      const range = {
        start: [start, '00:00:00'].join(' ').trim(),
        end: [end, '23:59:59'].join(' ').trim(),
      }
      this.onChangeDate(range)
    },

    // 时间范围改变
    onChangeDate(range) {
      this.dateRange.range = range
      this.query.startDate = range.start
      this.query.endDate = range.end
      this.$_search()
    },

    // 搜索
    $_search(type) {
      if (type !== 'pageNum') {
        this.query.pageNum = 1
      }
      if (JSON.stringify(this.$route.query) !== '{}') {
        updateURL.call(this)
      }
      this.getTableData()
      this.$_appendFilterListItem()
    },

    async getTableData() {
      try {
        this.tableLoading = true
        handleQueryParams({ type: 'set', query: this.query, key: 'alert' })
        const data = deepClone(this.query)
        // 保存查询参数
        const res = await getAlerts(data)
        this.tableDataTotal = res.data.total
        this.tableData = res.data.records
        this.$_clearTableSelected()
      } catch (e) {
        console.error(`获取告警管理报错：${e}`)
      }
      this.tableLoading = false
    },

    // 设置表格高度
    $_setTableHeight() {
      this.$nextTick(() => {
        const filterFn = () => {
          return -this.$refs.topHeader.filterHeight
        }
        this.tableHeight = setRemainingHeight(filterFn)
      })
    },

    // 表格选择事件
    $_tableSelected({ item, value, items }) {
      if (items) {
        this.selectedArray = value ? deepClone(items) : []
      } else if (value) {
        this.selectedArray.push(item)
      } else {
        const index = this.selectedArray.findIndex(v => v.id === item.id)
        this.selectedArray.splice(index, 1)
      }
    },

    //退出
    reback() {
      this.updateQuery = {
        alarmLevel: '',
        warnClassifyId: '',
        status: '',
      }
      this.updateShow = false
      this.$_search()
    },
    // 单击表格行
    clickTableRow(item) {
      if (this.$route.meta.buttonInfo['alert-detail']) {
        if (this.updateShow) return
        this.$router.push(`/alert/detail?id=${item.id}`)
      }
    },

    // 清除表格当前选择项
    $_clearTableSelected() {
      this.$refs.alearList && this.$refs.alearList.toggleSelectAll()
      this.selectedArray = []
    },

    // 批量修改状态
    $_changeStatus(val) {
      if (
        !this.updateQuery.alarmLevel &&
        !this.updateQuery.warnClassifyId &&
        !this.updateQuery.status
      ) {
        return
      }
      this.$swal({
        title: this.$t('alert.btn.status1'),
        text: this.$t('alert.hint.updateTip', [this.selectedArray.length]),
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: this.$t('action.confirm'),
        cancelButtonText: this.$t('action.cancel'),
        preConfirm: () => {
          return new Promise(resolve => {
            const params = Object.assign(this.updateQuery, {
              idList: this.selectedArray.map(item => item.id),
            })
            updateStatus(params)
              .then(res => {
                if (res.code === 200) {
                  this.$notify.info('success', this.$t('alert.hint.update'))
                }
                this.reback()
                this.$_clearTableSelected()
                resolve(true)
              })
              .catch(() => {
                resolve(false)
              })
          })
        },
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(result => {
        if (result.isConfirmed) {
          this.$swal.hideLoading()
        }
        this.updateQuery = {
          alarmLevel: '',
          warnClassifyId: '',
          status: '',
        }
      })
    },

    // 展示高级查询
    showDoQuery() {
      this.showAdvanceSearch = true
      const data = deepClone(this.query)
      this.$refs.alertsDrawer.setModel(data)
    },

    //  高级查询
    doQuery(params) {
      this.query = deepClone(params.advanceQuery)
      this.$_search()
    },

    // 添加查询条件
    $_appendFilterListItem() {
      const searchKeyList = [
        {
          key: 'alarmId',
          type: 'String',
          label: 'alert.headers.id',
        },
        {
          key: 'alarmTypeList',
          type: 'Array',
          mapKey: 'alertTypeMap',
          label: 'alert.headers.type',
        },
        {
          key: 'assetType',
          type: 'String',
          label: 'global.assetType',
          mapKey: 'assetTypeEnum',
        },
        {
          key: 'alarmLevelList',
          type: 'Array',
          mapKey: 'alertLevel',
          label: 'alert.headers.severity',
        },
        {
          key: 'statusList',
          type: 'Array',
          mapKey: 'alertStatus',
          label: 'alert.headers.status',
        },

        {
          key: 'warnClassifyIds',
          type: 'Array',
          mapKey: 'tagTypeMap',
          label: 'alert.headers.tag',
        },
        // {
        //   key: 'vehiclePlatformCode',
        //   type: 'String',
        //   label: '所属车企平台',
        //   mapKey: 'platFormMap',
        // },
      ]
      this.$_setTableHeight()
      handleFilterItem.call(this, searchKeyList)
    },

    // 清除某个查询条件
    $_clearFilter(item) {
      const bool = clearFilterItem.call(this, item)
      if (!bool) {
        this.$_search()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.status-color {
  width: 8px;
  height: 8px;
  background: red;
  border-radius: 50%;
  margin: 0 16px;
}
</style>
