<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>LK</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFBF19" offset="0%"></stop>
            <stop stop-color="#FFB800" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#A52531" offset="0%"></stop>
            <stop stop-color="#8E1F29" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#03664F" offset="0%"></stop>
            <stop stop-color="#005642" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FF6816" offset="0%"></stop>
            <stop stop-color="#FF5B01" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#FFBF18" offset="0%"></stop>
            <stop stop-color="#FFB800" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="LK">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <path d="M8,1 L19.2493463,1 C19.6639209,1 20,1.33441405 20,1.75512566 L20,13.2448743 C20,13.6619187 19.6665269,14 19.2493463,14 L8,14 L8,1 Z" id="Rectangle-1568" fill="url(#linearGradient-3)"></path>
            <path d="M1,1.75512566 C1,1.33808127 1.34413815,1 1.75702667,1 L4,1 L4,14 L1.75702667,14 C1.33893239,14 1,13.665586 1,13.2448743 L1,1.75512566 Z" id="Rectangle-1568" fill="url(#linearGradient-4)"></path>
            <rect id="Rectangle-1568" fill="url(#linearGradient-5)" x="4" y="1" width="3" height="13" rx="0.5"></rect>
            <path d="M9.5,3 C9.22385763,3 9,2.77614237 9,2.5 L9,2 L9.5,2 C9.77614237,2 10,2.22385763 10,2.5 C10,2.77614237 9.77614237,3 9.5,3 Z M18.5,3 C18.2238576,3 18,2.77614237 18,2.5 C18,2.22385763 18.2238576,2 18.5,2 L19,2 L19,2.5 C19,2.77614237 18.7761424,3 18.5,3 Z M18.5,12 C18.7761424,12 19,12.2238576 19,12.5 L19,13 L18.5,13 C18.2238576,13 18,12.7761424 18,12.5 C18,12.2238576 18.2238576,12 18.5,12 Z M9.5,12 C9.77614237,12 10,12.2238576 10,12.5 C10,12.7761424 9.77614237,13 9.5,13 L9,13 L9,12.5 C9,12.2238576 9.22385763,12 9.5,12 Z" id="Oval-157" fill="url(#linearGradient-6)"></path>
            <path d="M11.9999999,9.5 C11.9999999,9.22385763 11.773407,9 11.5,9 L11.9999999,9 C11.7238576,9 11.2914314,8.92403983 11.0253906,8.81542966 C11.0253906,8.81542966 10,8.4999999 10,8 C10,7.5000001 10.2033691,7.38562016 10.2033691,7.38562016 C10.3671938,7.17264803 10.417867,6.79756932 10.3154443,6.52530303 C10.3154443,6.52530303 9.90005537,5.5996678 10,5 C10.0999446,4.4003322 10.5707744,4.25100606 10.5707744,4.25100606 C10.8078292,4.11237924 11,4.2157526 11,4.49538898 L11,6.50461102 C11,6.7782068 11.1928101,7.09640503 11.453186,7.22659302 L11.546814,7.27340698 C11.7971017,7.39855085 12.1582031,7.34179688 12.3526878,7.14731216 L12.1473122,7.35268784 C12.3420963,7.15790372 12.3071899,6.90359497 12.046814,6.77340698 L11.5,6.5 L11.5,6 L12,6 C12.2761424,6 12.3071899,5.90359497 12.046814,5.77340698 L11.5,5.5 L11.5,5 L12,5 C12.2761424,5 12.6582031,4.84179688 12.8526878,4.64731216 L12.6473122,4.85268784 C12.8420963,4.65790372 13.2319336,4.5 13.5,4.5 L13.5,4.5 C13.7761424,4.5 14.096405,4.69281006 14.226593,4.95318604 L14.273407,5.04681396 C14.3985509,5.2971017 14.4304771,5.70856857 14.3417969,5.97460938 L14.1582031,6.52539063 C14.07083,6.78751014 14.2307129,7 14.5059123,7 L15.9940877,7 C16.2734953,7 16.6582031,6.84179688 16.8526878,6.64731216 L16.6473122,6.85268784 C16.8420963,6.65790372 16.8071899,6.40359497 16.546814,6.27340698 L16.453186,6.22659302 C16.2028983,6.10144915 15.7680664,6 15.5,6 L15.5,6 C15.2238576,6 15,5.76806641 15,5.5 L15,5.5 C15,5.22385763 15.1582031,4.84179688 15.3526878,4.64731216 L15.5,4.5 L15.5,5 C15.5,5.27614237 15.726593,5.5 16,5.5 L15.5,5.5 C15.7761424,5.5 16.2319336,5.5 16.5,5.5 L16.5,5.5 C16.7761424,5.5 17.096405,5.69281006 17.226593,5.95318604 L17.273407,6.04681396 C17.3985509,6.2971017 17.5196968,6.71962742 17.537102,6.99426903 C17.537102,6.99426903 17.6059485,7.47025774 17.5,8 C17.3940515,8.52974226 17.273967,8.58863041 17.273967,8.58863041 C17.1226592,8.81582356 17,9.21403503 17,9.50468445 L17,10.4953156 C17,10.7740451 16.7680664,11 16.5,11 L16.5,11 C16.2238576,11 16.096405,10.8071899 16.226593,10.546814 L16.273407,10.453186 C16.3985509,10.2028983 16.403595,9.80718994 16.273407,9.54681396 L16.226593,9.45318604 C16.1014491,9.2028983 16,9.23193359 16,9.5 L16,9.5 C16,9.77614237 15.903595,10.1928101 15.773407,10.453186 L15.726593,10.546814 C15.6014491,10.7971017 15.2680664,11 15,11 L15,11 C14.7238576,11 14.596405,10.8071899 14.726593,10.546814 L14.773407,10.453186 C14.8985509,10.2028983 15,9.76806641 15.0000001,9.5 L15.0000001,9.5 C15.0000001,9.22385763 14.7859651,9 14.4953157,9 L13.5046845,9 C13.2259549,9 12.9999999,9.23193359 12.9999999,9.5 L12.9999999,9.5 C12.9999999,9.77614237 12.9035949,10.1928101 12.7734069,10.453186 L12.726593,10.546814 C12.6014491,10.7971017 12.2680663,11 11.9999999,11 L11.9999999,11 C11.7238576,11 11.596405,10.8071899 11.726593,10.546814 L11.7734069,10.453186 C11.8985508,10.2028983 11.9999999,9.76806641 11.9999999,9.5 L11.9999999,9.5 Z" id="Rectangle-1067" fill="url(#linearGradient-6)"></path>
        </g>
    </g>
</svg>