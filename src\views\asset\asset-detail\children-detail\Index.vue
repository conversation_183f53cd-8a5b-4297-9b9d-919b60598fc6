<template>
  <div>
    <div
      class="d-flex align-center justify-space-between px-3"
      style="height: 48px"
    >
      <div class="d-flex">
        <v-btn class="mr-3" icon small @click="$router.go(-1)">
          <v-icon class="text--primary" size="20"> mdi-arrow-left </v-icon>
        </v-btn>
        <v-breadcrumbs class="pa-0" :items="breadItems"></v-breadcrumbs>
      </div>
      <!-- <v-tabs
        v-if="!isLite"
        v-model="currentTab"
        centered
        height="52"
        style="width: auto"
      >
        <v-tab class="mr-12">{{ $t('asset.tabName0') }}</v-tab>
        <v-tab>{{ $t('asset.tabName1') }}</v-tab>
      </v-tabs> -->
      <!-- <div class="d-flex align-center">
        <v-btn icon x-small @click="openDraw">
          <v-icon class="iconfont">icon-gaojichaxun</v-icon>
        </v-btn>

        <vsoc-date-range
          v-model="dateRange"
          :timeDisabled="true"
          no-title
          @input="dateChange"
          :presetFn="pickerFn"
        >
          <template v-slot:text="{ on, attrs }">
            <div v-bind="attrs" v-on="on" class="mx-2 text-content">
              {{ advanceQuery.startDate }} ~ {{ advanceQuery.endDate }}
            </div>
          </template>
        </vsoc-date-range>

        <v-tooltip bottom open-delay="300">
          <template v-slot:activator="{ on, attrs }">
            <v-btn icon x-small v-bind="attrs" v-on="on" @click="init">
              <v-icon class="iconfont">icon-shuaxin</v-icon>
            </v-btn>
          </template>
          <span>{{ showTips }}</span>
        </v-tooltip>
      </div> -->
      <Refresh
        :currentTab="1"
        :modeType="1"
        :refresh-date="refreshDate"
        @refresh="refresh"
        @showDrawer="openDraw"
      />
    </div>
    <v-tabs-items
      v-model="currentTab"
      style="max-height: calc(100vh - 48px - 52px)"
      class="bg-body overflow-auto scroll-bar-bg pa-3 pt-0"
    >
      <v-tab-item>
        <v-row dense>
          <v-col cols="12">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
              min-height="20rem"
            >
              <v-card>
                <v-card-title>
                  <span>{{ $t('asset.idps.posture') }}</span>
                  <v-spacer></v-spacer>
                  <!-- <v-chip
                    v-if="isExistDigitalTwin"
                    style="width: 32px"
                    label
                    class="active-chip active-chip-box cursor-pointer"
                    @click="showSignal"
                  >
                    <i
                      style="font-size: 16px"
                      class="iconfont icon-gengduo ml-n1 text--secondary"
                    ></i>
                  </v-chip> -->
                  <!-- <v-chip
                  label
                  small
                  v-if="statusEnum[currentObj.status]"
                  :text-color="statusEnum[currentObj.status].color"
                  class="opacity-b1 py-0 px-3 rounded-sm text-capitalize"
                >
                  {{
                    $generateName(
                      statusEnum[currentObj.status].text,
                      statusEnum[currentObj.status].enText,
                    )
                  }}
                </v-chip> -->
                </v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col cols="4" class="mt-n6">
                      <vsoc-chart
                        style="height: 18rem"
                        :option="boxChart"
                        echartId="boxChart"
                      ></vsoc-chart>
                    </v-col>
                    <v-col cols="8">
                      <v-row class="justify-end">
                        <v-col cols="4">
                          <div>{{ $t('asset.idps.posture') }}</div>
                          <div
                            :style="{
                              color: `${
                                carStatusMap[deviceTypeInfo.healthStatus].color
                              }`,
                            }"
                            style="color: #da1f1f"
                            class="text-n2xl font-weight-semibold"
                          >
                            {{ deviceTypeInfo.situation }}
                          </div>
                        </v-col>
                        <v-col cols="4">
                          <div>
                            {{ $t('asset.idps.status') }}
                          </div>
                          <div
                            v-if="deviceTypeInfo.assetAlarmInfoVos"
                            class="w-100 d-flex align-center text-white text-base font-weight-semibold alert-list mt-1"
                          >
                            <div
                              v-if="deviceTypeInfo.assetAlarmInfoVos.length > 0"
                              class="cursor-pointer"
                              @click.stop="goAlert"
                            >
                              <v-avatar
                                class="mr-2 icon-fb"
                                size="2.35rem"
                                v-for="(
                                  alarmItem, index
                                ) in deviceTypeInfo.assetAlarmInfoVos"
                                :key="index"
                                v-show-tips="
                                  alertLevel[alarmItem.alarmLevel] &&
                                  alertLevel[alarmItem.alarmLevel].text +
                                    ':' +
                                    alarmItem.alarmCount
                                "
                                :color="
                                  alertLevel[alarmItem.alarmLevel] &&
                                  alertLevel[alarmItem.alarmLevel].color
                                "
                              >
                                <span class="text-sm font-weight-medium">{{
                                  alarmItem.alarmCount
                                }}</span>
                              </v-avatar>
                            </div>
                            <div v-else>
                              {{ '' | dataFilter }}
                            </div>
                          </div>
                        </v-col>
                        <v-col cols="4">
                          <div>{{ $t('global.告警总数') }}</div>
                          <div class="text-n2xl font-weight-bold error--text">
                            {{ alertTotal }}
                          </div>
                        </v-col>
                      </v-row>
                      <v-row class="mt-10 mt-xl-13">
                        <v-col cols="4">
                          <div>
                            <v-chip
                              tile
                              label
                              x-small
                              color="primary"
                              class="me-2 pa-1"
                              >No.</v-chip
                            >
                            <span class="primary--text">{{
                              $t('model.device.id')
                            }}</span>
                          </div>
                          <div class="d-flex align-center">
                            <span
                              v-show-tips
                              class="d-inline-block text--primary text-ml text-truncate text-no-wrap"
                              style="max-width: 200px"
                            >
                              {{ deviceTypeInfo.deviceId | dataFilter }}
                            </span>
                            <v-btn
                              v-copy="deviceTypeInfo.deviceId"
                              v-show-tips="$t('action.copy')"
                              icon
                              class="ml-n1 mt-n1"
                            >
                              <vsoc-icon
                                type="fill"
                                size="middle"
                                icon="icon-fuzhi"
                              ></vsoc-icon>
                            </v-btn>
                          </div>
                        </v-col>
                        <v-col cols="4">
                          <div>VIN</div>
                          <div class="d-flex align-center">
                            <div
                              style="max-width: 160px"
                              class="text-ml color-base"
                            >
                              {{ deviceTypeInfo.vin }}
                            </div>
                            <v-btn
                              v-show-tips="
                                isDecipherVin
                                  ? $t('action.encode')
                                  : $t('action.decode')
                              "
                              icon
                              class="mt-n1"
                              @click="onVin"
                            >
                              <vsoc-icon
                                size="middle"
                                :icon="
                                  !isDecipherVin
                                    ? 'mdi-lock-check-outline'
                                    : 'mdi-lock-plus-outline'
                                "
                              ></vsoc-icon>
                            </v-btn>
                          </div>
                        </v-col>
                        <v-col cols="3">
                          <div>{{ $t('asset.headers.model') }}</div>
                          <div>
                            <span class="text-ml color-base">
                              {{ deviceTypeInfo.vehicleModel }}
                            </span>
                          </div>
                        </v-col>
                        <v-col cols="1" v-if="isExistDigitalTwin">
                          <v-btn
                            icon
                            class="bg-body"
                            rounded
                            @click="isDigitalTwinShow = !isDigitalTwinShow"
                          >
                            <vsoc-icon
                              type="fill"
                              class="text--primary"
                              :icon="
                                !isDigitalTwinShow
                                  ? 'icon-zhankai'
                                  : 'icon-shouqi'
                              "
                            ></vsoc-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                      <!-- <v-row class="pb-8">
                        <template
                          v-for="(
                            item, index
                          ) in deviceTypeInfo.deviceTypeSignals"
                        >
                          <v-col
                            :key="index"
                            cols="4"
                            class="mt-3"
                            v-if="index < 6"
                          >
                            <div>{{ item.name }}</div>
                            <div
                              v-show-tips
                              class="font-weight-medium text--primary text-ml mt-1 text-no-wrap"
                            >
                              {{ item.value }}
                            </div>
                          </v-col>
                        </template>
                      </v-row> -->
                      <!-- <v-row class="justify-end pb-8">
                        <v-col cols="4">
                          <div>{{ $t('asset.idps.os') }}</div>
                          <div
                            class="font-weight-medium text--primary text-ml mt-1 text-no-wrap"
                          >
                            Linux
                          </div>
                        </v-col>
                        <v-col>
                          <div>{{ $t('asset.headers.lastActiveTime') }}</div>
                          <div
                            class="font-weight-medium text--primary text-ml mt-1 text-no-wrap"
                          >
                            2023-09-05 14:51:00
                          </div>
                        </v-col>
                      </v-row> -->
                    </v-col>
                  </v-row>
                </v-card-text>
                <asset-event-detail
                  v-show="isDigitalTwinShow"
                  :data="currentLineData"
                  :vin="deviceTypeInfo.vin"
                  :lastReceiveDate="lastReceiveDate"
                  :assetType="'0'"
                  mode="digital-twin"
                ></asset-event-detail>
              </v-card>
            </v-skeleton-loader>
          </v-col>
          <!-- <v-col cols="4">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <v-card class="h-full w-100">
                <v-card-text class="pa-9 h-100 d-flex flex-column">
                  <div class="d-flex">
                    <div class="d-flex">
                      <v-avatar color="error" size="42" rounded="lg">
                        <vsoc-icon
                          icon="icon-gaojingjibiebiaozhi"
                          type="fill"
                          class="white--text"
                          size="28px"
                        ></vsoc-icon>
                      </v-avatar>
                      <div class="ml-6">
                        <span
                          class="text--primary text-title font-weight-medium"
                          >{{ $t('analytics.totalAlerts') }}</span
                        >
                        <div class="secondary--text mt-1">Total assets</div>
                      </div>
                    </div>
                    <v-spacer></v-spacer>
                  </div>
                  <div class="mt-6 text-n5xl text--primary font-weight-medium">
                    {{ alertTotal }}
                  </div>
                  <vsoc-chart
                    class="flex-1 mb-n9 mt-n9"
                    echartId="biear"
                    :option="lineOption"
                  ></vsoc-chart>
                </v-card-text>
              </v-card>
            </v-skeleton-loader>
          </v-col> -->
          <!-- <v-col cols="12" v-if="isExistDigitalTwin">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <asset-event-detail
                v-if="isExistDigitalTwin"
                :data="currentLineData"
                :assetType="'0'"
                mode="digital-twin"
              ></asset-event-detail>
            </v-skeleton-loader>
          </v-col> -->
          <v-col cols="4" style="height: 23.0833rem">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <ring-chart
                echartId="levelChart"
                :title="$t('analytics.alertLevel')"
                :color="alertColor"
                :list="levelList"
                :subtext="$t('analytics.totalAlerts')"
              ></ring-chart>
            </v-skeleton-loader>
          </v-col>
          <v-col cols="4">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <ring-chart
                :color="alarmStatusColor"
                echartId="activeChart"
                :title="$t('analytics.alertStatus')"
                :list="activeList"
                :subtext="$t('analytics.totalAlerts')"
              ></ring-chart>
            </v-skeleton-loader>
          </v-col>
          <v-col cols="4">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <ring-chart
                echartId="typeChart"
                :title="$t('analytics.alertType')"
                :list="typeList"
                :subtext="$t('analytics.drawer.alertType')"
              ></ring-chart>
            </v-skeleton-loader>
          </v-col>
          <v-col cols="8" style="height: 31rem">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <flow-statistics
                :startDate="advanceQuery.startDate"
                :endDate="advanceQuery.endDate"
                :list="collectionData"
                :color="alertColor"
              ></flow-statistics>
            </v-skeleton-loader>
          </v-col>
          <v-col cols="4">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <v-card class="h-full d-flex flex-column">
                <v-card-title class="align-start text-xxl">
                  <span>{{ $t('analytics.safeEventTop5') }}</span>
                </v-card-title>

                <v-card-text
                  class="flex-1 mt-3 pb-0"
                  v-empty-chart="totalEarning.length === 0"
                >
                  <!-- @click="onSearch(earning, 'warnClassifyIds')" -->
                  <template v-if="totalEarning.length !== 0">
                    <div
                      v-for="(earning, index) in totalEarning"
                      :key="index"
                      class="d-flex flex-column cursor-pointer"
                      style="height: 20%"
                    >
                      <div class="d-flex">
                        <div
                          v-show-tips
                          class="text-overflow-hide text--primary"
                          style="max-width: calc(100% - 8.5rem)"
                        >
                          <v-icon :color="earning.color" class="iconfont mr-2"
                            >icon-gaojingjibiebiaozhi</v-icon
                          >
                          {{ earning.title }}
                        </div>
                        <v-spacer></v-spacer>

                        <p
                          class="text--primary font-weight-medium mb-2 text-right"
                        >
                          <span>
                            <span class="font-weight-medium">{{
                              earning.earning | numberToFormat
                            }}</span>
                            <strong class="ml-5 text--secondary">{{
                              earning.format
                            }}</strong>
                          </span>
                        </p>
                      </div>
                      <v-progress-linear
                        :value="earning.progress"
                        :color="earning.color"
                        background-color="bg-body"
                        rounded
                        :height="10 | getRoundSize"
                      ></v-progress-linear>
                    </div>
                  </template>
                </v-card-text>
              </v-card>
            </v-skeleton-loader>
          </v-col>
          <v-col cols="6" style="height: 33rem">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <radar-chart
                echartId="statusRadar"
                :list="alarmTypes"
                :title="$t('asset.idps.typeDis')"
              ></radar-chart>
            </v-skeleton-loader>
          </v-col>
          <v-col cols="6" style="height: 33rem">
            <v-skeleton-loader
              :dark="$vuetify.theme.dark"
              :loading="isLoading"
              type="image"
              class="mx-auto h-full w-100"
            >
              <radar-chart
                echartId="levelRadar"
                :list="statusList"
                :title="$t('asset.idps.alertDis')"
              ></radar-chart>
            </v-skeleton-loader>
          </v-col>
        </v-row>
      </v-tab-item>
      <!-- <v-tab-item>
        <v-row dense>
          <v-col cols="4">
            <v-card class="pa-6 d-flex">
              <v-avatar
                size="4.6rem"
                rounded="lg"
                class="primary--text opacity-b1 mr-3"
              >
                <vsoc-icon
                  class="primary--text"
                  size="2.75rem"
                  type="fill"
                  :icon="currentObj.icon"
                ></vsoc-icon>
              </v-avatar>
              <div class="flex-1">
                <div class="d-flex">
                  <div>
                    <div class="text-n2xl primary--text">
                      {{ currentObj.text }}
                    </div>
                    <div class="text-ml text--secondary">T-102156</div>
                  </div>
                  <v-spacer></v-spacer>
                  <v-chip
                    label
                    small
                    v-if="statusEnum[currentObj.status]"
                    :text-color="statusEnum[currentObj.status].color"
                    class="opacity-b1 py-0 px-3 rounded-sm"
                  >
                    {{
                      $generateName(
                        statusEnum[currentObj.status].text,
                        statusEnum[currentObj.status].enText,
                      )
                    }}
                  </v-chip>
                </div>
                <v-row class="justify-end pt-8">
                  <v-col>
                    <div>{{ $t('asset.idps.version') }}</div>
                    <div class="text--primary text-ml mt-1 text-no-wrap">
                      V1.21
                    </div>
                  </v-col>
                  <v-col>
                    <div>{{ $t('asset.headers.firstRegistrationTime') }}</div>
                    <div class="text--primary text-ml mt-1 text-no-wrap">
                      2023-09-05 14:50:59
                    </div>
                  </v-col>
                </v-row>
                <v-row class="justify-end pb-4">
                  <v-col>
                    <div>{{ $t('asset.idps.os') }}</div>
                    <div class="text--primary text-ml mt-1 text-no-wrap">
                      Linux
                    </div>
                  </v-col>
                  <v-col>
                    <div>{{ $t('asset.headers.lastActiveTime') }}</div>
                    <div class="text--primary text-ml mt-1 text-no-wrap">
                      2023-09-05 14:51:00
                    </div>
                  </v-col>
                </v-row>
              </div>
            </v-card>
          </v-col>
          <v-col cols="4">
            <regular-distribution></regular-distribution>
          </v-col>
          <v-col cols="4">
            <rule-dynamics></rule-dynamics>
          </v-col>
        </v-row>
        <v-row dense class="mt-1">
          <v-col cols="6">
            <storage-chart></storage-chart>
          </v-col>
          <v-col cols="6">
            <storage-table></storage-table>
          </v-col>
        </v-row>
        <v-row dense class="mt-1">
          <v-col cols="6" style="height: 23rem">
            <event-table
              :title="$t('asset.idpsTab2.eventDistribution')"
              :list="statusList2"
            ></event-table>
          </v-col>
          <v-col cols="6">
            <event-table
              :title="$t('asset.idpsTab2.ipDistribution')"
              :list="cityList"
            ></event-table>
          </v-col>
        </v-row>
        <v-row dense class="mt-1">
          <v-col cols="6">
            <strategy-table></strategy-table>
          </v-col>
          <v-col cols="6">
            <event-detail-table></event-detail-table>
          </v-col>
        </v-row>
      </v-tab-item> -->
    </v-tabs-items>

    <idps-drawer
      ref="idpsDrawer"
      :value="isShow"
      @input="isShow = false"
      @do-query="doQuery"
    ></idps-drawer>
    <vsoc-dialog
      v-model="signalDialog.show"
      width="800px"
      :max-height="600"
      custom-class="email-dialog"
      :hideConfirmBtn="true"
    >
      <template #title>
        <div class="color-base font-weight-semibold text-title w-50">
          {{ $t('asset.axis.view') }}
        </div>
        <div class="w-30">
          <v-text-field
            v-model="signalDialog.search"
            outlined
            dense
            hide-details
            :label="$t('asset.axis.queryField')"
            prepend-inner-icon="mdi-magnify"
          ></v-text-field>
        </div>
      </template>
      <v-data-table
        ref="actionsTable"
        fixed-header
        height="400"
        item-key="code"
        hide-default-footer
        :headers="headers"
        :search="signalDialog.search"
        :items="signalDialog.tableData"
      >
        <template v-slot:item.name="{ item }">
          <div v-show-tips style="width: 120px">
            {{ item.name | dataFilter }}
          </div>
        </template>
        <template v-slot:item.value="{ item }">
          <div v-show-tips style="width: 120px">
            {{ item.value | dataFilter }}
          </div>
        </template>
        <template v-slot:item.description="{ item }">
          <div v-show-tips style="width: 200px">
            {{ item.description | dataFilter }}
          </div>
        </template>
      </v-data-table>
    </vsoc-dialog>
  </div>
</template>

<script>
import { getVehicleDetails, getVin } from '@/api/asset/index'
// import { getIdpsStatistics } from '@/api/asset'
import { getIdpsStatistics } from '@/api/analytics'
import VsocChart from '@/components/VsocChart.vue'
import VsocDialog from '@/components/VsocDialog.vue'
import VsocDateRange from '@/components/vsoc-date-range/VsocDateRange.vue'
import { RANGE_STR } from '@/components/vsoc-date-range/constants'
import { alarmStatusColor, alertColor } from '@/plugins/systemColor'
import { idpsStatus } from '@/util/enum'
import { getDateDiff, toDate } from '@/util/filters'
import { deepClone, hexToRgb } from '@/util/utils'
import IdpsDrawer from '@/views/asset/asset-detail/children-detail/components/IdpsDrawer'
import Refresh from '@/views/dashboards/analytics/Refresh.vue'
import { endOfDay, startOfDay, subDays, subHours } from 'date-fns'
import AssetEventDetail from '../../asset-info/components/AssetEventDetail.vue'
import EventDetailTable from './components/EventDetailTable.vue'
import EventTable from './components/EventDistribution.vue'
import FlowStatistics from './components/FlowStatistics.vue'
import RadarChart from './components/RadarChart.vue'
import RegularDistribution from './components/RegularDistribution.vue'
import RingChart from './components/RingChart.vue'
import RuleDynamics from './components/RuleDynamics'
import StorageChart from './components/StorageChart.vue'
import StorageTable from './components/StorageTable.vue'
import StrategyTable from './components/StrategyTable'

export default {
  name: 'AssetChildren',
  components: {
    RegularDistribution,
    RuleDynamics,
    FlowStatistics,
    StorageChart,
    StorageTable,
    EventTable,
    StrategyTable,
    EventDetailTable,
    VsocChart,
    RingChart,
    RadarChart,
    VsocDateRange,
    IdpsDrawer,
    Refresh,
    VsocDialog,
    AssetEventDetail,
  },
  data() {
    return {
      lastReceiveDate: '',
      isDecipherVin: false,
      isExistDigitalTwin: false,
      isDigitalTwinShow: false,
      currentLineData: {
        id: null,
        eventName: '最近活动',
        eventTime: '2024-04-26 14:50:51',
        vehicleId: null,
        vehicleVin: null,
        signalData: null,
        signalDataList: [],
        eventId: null,
        pageSize: 1,
        description: null,
        firstEventDate: '2024-04-26 14:50:51',
        lastEventDate: '2024-04-26 14:50:51',
        eventCount: null,
        longitude: 121.467694,
        latitude: 31.222272,
        type: 'RecentActivity',
        alarmLevel: null,
        hisEventTime: null,
        alarmFields: [],
        specialFields: [],
        defaultFields: [
          {
            id: 'processing_time',
            name: '处理时间',
            value: '2024-04-26 14:50:51',
            signalValueType: '4',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: 'processing_time',
            constrainsObject: null,
            relationType: '3',
          },
          {
            id: 'object_array_test',
            name: '对象数组测试',
            value: [{ 'Car-碰撞值': 4 }],
            signalValueType: '6',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '对象数组测试',
            constrainsObject: null,
            relationType: '3',
            list: [{ 'Car-碰撞值': 4 }],
          },
          {
            id: 'vehicle_vehicleidentification_model',
            name: '车辆型号',
            value: 'BWM',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '车辆的型号',
            constrainsObject: null,
            relationType: '3',
          },
          {
            id: 'vehicle_message_senttimestamp',
            name: '发送时间戳',
            value: '2024-04-26 14:50:49',
            signalValueType: '4',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '消息源发送消息的时间戳',
            constrainsObject: null,
            relationType: '3',
          },
          {
            id: 'vehicle_location_currentlocation_city',
            name: '当前车辆位置城市',
            value: 'ShangHai',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '根据GPS定位得出的当前车辆城市',
            constrainsObject: null,
            relationType: '3',
          },
          {
            id: 'vehicle_vehicleidentification_id',
            name: '车辆ID',
            value: 'obucn7m132e219397',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '车辆的唯一标识符',
            constrainsObject: null,
            relationType: '3',
          },
          {
            id: 'vehicle_powertrain_powersource_type',
            name: '引擎类型',
            value: 'STRAIGHT-SIX',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '车辆的发动机类型',
            constrainsObject: null,
            relationType: '3',
          },
        ],
        fullDigitalTwin: [
          {
            id: 'processing_time',
            name: '处理时间',
            value: '2024-04-26 14:50:51',
            signalValueType: '4',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: 'processing_time',
            constrainsObject: null,
            relationType: '3',
            newValue: '2024-04-26 14:50:51',
            color: false,
            _key: '[native Symbol Symbol(processing_time)]',
          },
          {
            id: 'object_array_test',
            name: '对象数组测试',
            value: '1项',
            signalValueType: '6',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '对象数组测试',
            constrainsObject: null,
            relationType: '3',
            list: [{ 'Car-碰撞值': 4 }],
            newValue: '[{"Car-碰撞值":4}]',
            color: false,
            _key: '[native Symbol Symbol(object_array_test)]',
          },
          {
            id: 'vehicle_vehicleidentification_model',
            name: '车辆型号',
            value: 'BWM',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '车辆的型号',
            constrainsObject: null,
            relationType: '3',
            newValue: 'BWM',
            color: false,
            _key: '[native Symbol Symbol(vehicle_vehicleidentification_model)]',
          },
          {
            id: 'vehicle_message_senttimestamp',
            name: '发送时间戳',
            value: '2024-04-26 14:50:49',
            signalValueType: '4',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '消息源发送消息的时间戳',
            constrainsObject: null,
            relationType: '3',
            newValue: '2024-04-26 14:50:49',
            color: false,
            _key: '[native Symbol Symbol(vehicle_message_senttimestamp)]',
          },
          {
            id: 'vehicle_location_currentlocation_city',
            name: '当前车辆位置城市',
            value: 'ShangHai',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '根据GPS定位得出的当前车辆城市',
            constrainsObject: null,
            relationType: '3',
            newValue: 'ShangHai',
            color: false,
            _key: '[native Symbol Symbol(vehicle_location_currentlocation_city)]',
          },
          {
            id: 'vehicle_vehicleidentification_id',
            name: '车辆ID',
            value: 'obucn7m132e219397',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '车辆的唯一标识符',
            constrainsObject: null,
            relationType: '3',
            newValue: 'obucn7m132e219397',
            color: false,
            _key: '[native Symbol Symbol(vehicle_vehicleidentification_id)]',
          },
          {
            id: 'vehicle_location_currentlocation_longitude',
            name: '经度',
            value: 121.467694,
            signalValueType: '2',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '经度',
            constrainsObject: null,
            relationType: '3',
            newValue: 121.467694,
            color: false,
            _key: '[native Symbol Symbol(vehicle_location_currentlocation_longitude)]',
          },
          {
            id: 'vehicle_powertrain_powersource_type',
            name: '引擎类型',
            value: 'STRAIGHT-SIX',
            signalValueType: '0',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '车辆的发动机类型',
            constrainsObject: null,
            relationType: '3',
            newValue: 'STRAIGHT-SIX',
            color: false,
            _key: '[native Symbol Symbol(vehicle_powertrain_powersource_type)]',
          },
          {
            id: 'vehicle_location_currentlocation_latitude',
            name: '纬度',
            value: 31.222272,
            signalValueType: '2',
            signalTime: '2024-04-26 14:50:49',
            signalType: null,
            description: '纬度',
            constrainsObject: null,
            relationType: '3',
            newValue: 31.222272,
            color: false,
            _key: '[native Symbol Symbol(vehicle_location_currentlocation_latitude)]',
          },
        ],
        statisticsData: [],
        color: null,
        location: '上海市黄浦区南昌路328号淮海坊',
      },
      signalDialog: {
        show: false,
        tableData: [],
        search: '',
      },
      alertColor: alertColor,
      alarmStatusColor: alarmStatusColor,
      deviceTypeInfo: {
        score: 0,
        situation: '',
        deviceTypeName: '',
        healthStatus: '0',
        assetAlarmInfoVos: [],
        deviceTypeSignals: [],
      },
      isLoading: true,
      currentTab: 0,
      refreshDate: 0,
      isShow: false,
      pickerFn: newDate => {
        return [
          {
            label: this.$t('enums.datePresets.last24'),
            range: [subHours(newDate, 24), newDate],
          },
          {
            label: this.$t('enums.datePresets.last7'),
            range: [
              startOfDay(subDays(endOfDay(newDate), 6)),
              endOfDay(newDate),
            ],
          },
          {
            label: this.$t('enums.datePresets.last30'),
            range: [
              startOfDay(subDays(endOfDay(newDate), 29)),
              endOfDay(newDate),
            ],
          },
        ]
      },
      // dateRange: {
      //   start: null,
      //   end: null,
      // },
      dateRange: {
        start: '',
        end: '',
      },
      advanceQuery: {
        vehicleId: '',
        deviceType: '',
        alarmLevelList: ['0', '1', '2', '3', '4'],
        statusList: ['0', '1', '2'],
        startDate: toDate(startOfDay(subDays(new Date(), 29))),
        endDate: toDate(endOfDay(new Date())),
      },
      currentObj: {},
      alarmTypes: [],
      statusList: [],
      statusList2: [
        {
          title: '疑似远程入侵',
          progress: 26.7,
          count: 336,
          color: '#533DF1',
        },
        {
          title: '入口流量异常',
          progress: 23.02,
          count: 248,
          color: '#40CD6E',
        },
        {
          title: '未知用户',
          progress: 19.88,
          count: 178,
          color: '#FBD82C',
        },
        {
          title: '未知进程',
          progress: 16.75,
          count: 89,
          color: '#44E2FE',
        },
        {
          title: '未知端口',
          progress: 9.14,
          count: 16,
          color: '#1B84FF',
        },
      ],
      cityList: [
        {
          title: '110.1.12.1',
          progress: 26.7,
          count: 336,
          color: '#533DF1',
        },
        {
          title: '10.111.12.3',
          progress: 23.02,
          count: 248,
          color: '#40CD6E',
        },
        {
          title: '10.111.12.223',
          progress: 19.88,
          count: 178,
          color: '#FBD82C',
        },
        {
          title: '10.111.122.13',
          progress: 16.75,
          count: 89,
          color: '#44E2FE',
        },
        {
          title: '10.111.121.13',
          progress: 9.14,
          count: 16,
          color: '#1B84FF',
        },
      ],
      levelList: [
        // { name: '严重', value: 481, percent: '75.51%', color: '#313CA6' },
        // { name: '中', value: 98, percent: '15.38%', color: '#0082D6' },
        // { name: '高', value: 49, percent: '7.69%', color: '#55D1FD' },
        // { name: '低', value: 7, percent: '1.41%', color: '#3D4A66' },
      ],
      activeList: [
        // { name: '误报', percent: '60%', value: 9962, color: '#313CA6' },
        // { name: '待处理', percent: '37%', value: 6241, color: '#0082D6' },
        // { name: '重复警告', percent: '1%', value: 226, color: '#55D1FD' },
        // { name: '不是问题', percent: '1%', value: 213, color: '#3D4A66' },
        // { name: '处理中', percent: '0%', value: 14, color: '#8F9AB2' },
        // { name: '有效事件', percent: '0%', value: 1, color: '#B4BBCC' },
      ],
      typeList: [
        // {
        //   name: 'vehicle',
        //   value: 11582,
        //   icon: 'icon-Vehicles',
        //   color: '#214EA8',
        //   percent: '100%',
        // },
      ],
      totalEarning: [
        // {
        //   name: 'CPU负载过高',
        //   alarmLevel: '2',
        //   alarmLevelName: '中',
        //   detectorId: '1663102593355743232',
        //   alarmCount: 0,
        //   alarmPercentage: '43.52%',
        //   count: 1356,
        //   title: 'CPU负载过高',
        //   color: '#FBD82C',
        //   earning: 1356,
        //   progress: '43.52',
        //   level: '2',
        // },
        // {
        //   name: '测试特征数据类型',
        //   alarmLevel: '0',
        //   alarmLevelName: '严重',
        //   detectorId: '1791399695189278720',
        //   alarmCount: 0,
        //   alarmPercentage: '26.19%',
        //   count: 816,
        //   title: '测试特征数据类型',
        //   color: '#DA1F1F',
        //   earning: 816,
        //   progress: '26.19',
        //   level: '0',
        // },
        // {
        //   name: '测试特征不同值特征值异常',
        //   alarmLevel: '2',
        //   alarmLevelName: '中',
        //   alarmCount: 0,
        //   alarmPercentage: '13.70%',
        //   count: 427,
        //   title: '测试特征不同值特征值异常',
        //   color: '#FBD82C',
        //   earning: 427,
        //   progress: '13.70',
        //   level: '2',
        // },
        // {
        //   name: '未知用户',
        //   alarmLevel: '0',
        //   alarmLevelName: '严重',
        //   detectorId: '1662954772300300288',
        //   alarmCount: 0,
        //   alarmPercentage: '10.69%',
        //   count: 333,
        //   title: '未知用户',
        //   color: '#DA1F1F',
        //   earning: 333,
        //   progress: '10.69',
        //   level: '0',
        // },
        // {
        //   name: '测试特征数据类型特征值异常',
        //   alarmLevel: '2',
        //   alarmLevelName: '中',
        //   alarmCount: 0,
        //   alarmPercentage: '5.91%',
        //   count: 184,
        //   title: '测试特征数据类型特征值异常',
        //   color: '#FBD82C',
        //   earning: 184,
        //   progress: '5.91',
        //   level: '2',
        // },
      ],
      collectionData: [],
      alertTotal: 0,
    }
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('asset.axis.headers.signalName'),
          value: 'name',
          width: 120,
        },
        {
          text: this.$t('asset.axis.headers.value'),
          value: 'value',
          width: 120,
        },
        {
          text: this.$t('asset.axis.headers.signalSource'),
          value: 'signalTime',
          width: 160,
        },
        {
          text: this.$t('asset.axis.headers.desc'),
          value: 'description',
          width: 200,
        },
      ]
    },
    carStatusMap() {
      return this.$store.state.enums.enums.HealthStatus
    },
    isLite() {
      const userInfo = this.$store.getters['userInfo']
      return userInfo.level == '0'
    },
    assetTypeEnum() {
      return this.$store.state.enums.enums.AssetType
    },
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    alarmStatus() {
      return this.$store.state.enums.enums.AlarmStatus
    },
    showTips() {
      return `${this.$t('global.updateDiff', [getDateDiff(this.refreshDate)])}`
    },
    // 告警总数option
    lineOption() {
      return {
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          show: false,
        },
        yAxis: {
          type: 'value',
          show: false,
        },
        // backgroundColor: '#ff0',
        grid: {
          top: 10,
          left: '-12%',
          right: '-8%',
          bottom: '-8%',
          containLabel: true,
        },
        series: [
          {
            lineStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(218, 31, 31, 0)', // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: '#DA1F1F', // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(218, 31, 31, 0)', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              width: 4,
            },
            data: [5, 10, 9, 18, 16, 28, 25],
            type: 'line',

            showSymbol: false,
            smooth: true,
            symbolSize: 10,
          },
        ],
      }
    },
    boxChart() {
      //获取圆上面某点的坐标(x0,y0表示坐标，r半径，angle角度)
      function getCirlPoint(x0, y0, r, angle) {
        let x1 = x0 + r * Math.cos((angle * Math.PI) / 180)
        let y1 = y0 + r * Math.sin((angle * Math.PI) / 180)
        return {
          x: x1,
          y: y1,
        }
      }
      let angle = 0 //角度，用来做简单的动画效果的
      let value = Number(this.deviceTypeInfo.score)
      let code = this.deviceTypeInfo.deviceTypeName
      let status = 0
      if (value >= 95 && value <= 100) {
        status = 0
      } else if (value >= 85 && value <= 94) {
        status = 1
      } else if (value >= 65 && value <= 84) {
        status = 2
      } else if (value <= 64) {
        status = 3
      }
      let color = this.$store.state.enums.enums.HealthStatus[status].color
      let option = {
        title: {
          text: `{textColor|${value}}\n${code}`,
          left: 'center',
          top: 'center',
          textStyle: {
            rich: {
              textColor: {
                color: color,
                fontSize: 50,
              },
              point: {
                fontSize: 14,
                lineHeight: 22,
                color: '#A1A6B1',
                verticalAlign: 'top',
                padding: [10, -10, 0, 0],
              },
            },
          },
        },
        legend: {
          show: false,
        },
        series: [
          {
            name: '分数',
            type: 'pie',
            radius: ['60%', '82%'],
            silent: true,
            clockwise: true,
            startAngle: 180,
            z: 0,
            zlevel: 0,
            label: {
              position: 'center',
            },
            data: [
              {
                value: value,
                name: '',
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: `rgb(${hexToRgb(color)}, 1)`, // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: `rgb(${hexToRgb(color)}, 0.2)`, // 100% 处的颜色
                      },
                    ],
                    global: false, // 缺省为 false
                  },
                },
              },
              {
                value: 100 - value,
                name: '',
                label: {
                  show: false,
                },
                itemStyle: {
                  color: '#E6EAF2',
                },
              },
            ],
          },
          {
            name: '分割线',
            type: 'gauge',
            radius: '110%',
            center: ['50%', '50%'],
            startAngle: 0,
            endAngle: 360.0,
            splitNumber: 65,
            // hoverAnimation: true,
            axisTick: {
              show: false,
            },
            // splitLine: {
            //   length: 30,
            //   lineStyle: {
            //     width: 5,
            //     color: '#fff',
            //   },
            // },
            axisLabel: {
              show: false,
            },
            pointer: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                opacity: 0,
              },
            },
            detail: {
              show: false,
            },
            data: [
              {
                value: 100,
                name: '',
              },
            ],
          },
          // {
          //   name: '背景线',
          //   type: 'pie',
          //   silent: true,
          //   startAngle: 0,
          //   z: 1,
          //   clockWise: true,
          //   hoverAnimation: false,
          //   radius: ['85%', '100%'],
          //   center: ['50%', '50%'],
          //   label: {
          //     show: false,
          //   },
          //   itemStyle: {
          //     label: {
          //       show: false,
          //     },
          //     labelLine: {
          //       show: false,
          //     },

          //     color: '#0b5263',
          //     borderWidth: 14,
          //     borderColor: '#fff',
          //   },
          //   data: [50, 50, 50, 50],
          // },
          // 第1个弧 点-线-点
          {
            name: 'ring5', // 上右点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, 278 + angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2', //绿
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5',
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95,
                  startAngle: ((280 + -angle) * Math.PI) / 180,
                  endAngle: ((-10 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#E6EAF2',
                  fill: 'transparent',
                  lineWidth: 1,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', // 右上点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, -8 + angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2', //绿
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
          // 第2个弧 点-线-点
          {
            name: 'ring5', // 左下点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, 8 + angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2', //绿
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', // 线
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95,
                  startAngle: ((10 + -angle) * Math.PI) / 180,
                  endAngle: ((80 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#E6EAF2',
                  fill: 'transparent',
                  lineWidth: 1,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', // 下右点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, 82 + angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2', //绿
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
          // 第3个弧 点-线-点
          {
            name: 'ring5', // 下左点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, 98 + angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2',
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', // 线
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95,
                  startAngle: ((100 + -angle) * Math.PI) / 180,
                  endAngle: ((170 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#E6EAF2',
                  fill: 'transparent',
                  lineWidth: 1,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', // 左下点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, 172 + angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2',
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
          // 第4个弧 点-线-点
          {
            name: 'ring5', // 左上点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, 188 + -angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2', //绿
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', // 线
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95,
                  startAngle: ((190 + -angle) * Math.PI) / 180,
                  endAngle: ((260 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#E6EAF2',
                  fill: 'transparent',
                  lineWidth: 1,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', // 上左点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.95
              let point = getCirlPoint(x0, y0, r, 262 + -angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#E6EAF2', //绿
                  fill: 'transparent',
                },
                silent: true,
              }
            },
            data: [0],
          },
        ],
      }

      return option
    },
    statusEnum() {
      return idpsStatus
    },
    unitItems() {
      return [
        {
          text: 'T-BOX',
          status: '0',
          icon: 'icon-T-Box',
        },
        {
          text: 'IVI',
          status: '0',
          icon: 'icon-IVI',
        },
        {
          text: '网关',
          status: '0',
          icon: 'icon-wangguan',
        },
        {
          text: '域控-101',
          status: '1',
          icon: 'icon-yukong',
        },
      ]
    },
    breadItems() {
      const routerList = this.$router.getRoutes()
      let first = routerList.find(
        v => v.name === this.$route.meta.navActiveLink,
      )
      let second = routerList.find(v => v.meta.code === 'asset-detail')
      let third = routerList.find(v => v.name === this.$route.meta.title)
      return [
        {
          text: this.$generateMenuTitle(first.meta),
          disabled: true,
          href: '#',
        },
        {
          text: this.$generateMenuTitle(second.meta),
          disabled: true,
          href: '#',
        },
        {
          text: this.$generateMenuTitle(third.meta),
          disabled: false,
          href: '#',
        },
      ]
    },
  },
  mounted() {
    this.getInfo()
    this.loadData()
  },
  methods: {
    async getInfo() {
      let res = await getVehicleDetails({ id: this.$route.query.id })
      this.lastReceiveDate = res.data.lastReceiveDate
    },
    async onVin() {
      try {
        const { data } = await getVin({ vin: this.deviceTypeInfo.vin })
        this.deviceTypeInfo.vin = data
        this.isDecipherVin = !this.isDecipherVin
      } catch (err) {
        console.log('vin报错', err)
      }
    },
    showSignal() {
      this.signalDialog.show = true
      this.signalDialog.tableData = this.deviceTypeInfo.deviceTypeSignals
    },
    refresh(obj) {
      this.advanceQuery.startDate = obj.start
      this.advanceQuery.endDate = obj.end
      this.$nextTick(() => {
        this.loadData()
      })
    },
    dateChange(range) {
      this.advanceQuery.startDate = range.start
      this.advanceQuery.endDate = range.end
      this.loadData()
    },
    openDraw() {
      this.isShow = true
      const data = deepClone(this.advanceQuery)
      this.$refs.idpsDrawer.setModel(data)
    },
    // 点击告警状态
    goAlert() {
      const alarmLevelList = this.deviceTypeInfo.assetAlarmInfoVos
        .filter(v => v.alarmCount > 0)
        .map(t => t.alarmLevel)
      if (alarmLevelList.length === 0) {
        return
      }
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          vehicleId: this.$route.query.id,
          alarmLevelList: JSON.stringify(alarmLevelList),
          statusList: JSON.stringify(['0', '1']),
          deviceType: this.$route.query.deviceType,
        },
      })
    },
    RANGE_STR,
    doQuery(params) {
      this.advanceQuery.alarmLevelList = deepClone(params.alarmLevelList)
      this.advanceQuery.statusList = deepClone(params.statusList)
      this.loadData()
    },
    async loadData() {
      try {
        this.isLoading = true
        this.$showLoading()
        this.refreshDate = new Date().getTime()
        this.advanceQuery.vehicleId = this.$route.query.id
        this.advanceQuery.deviceType = this.$route.query.deviceType
        const { data } = await getIdpsStatistics(this.advanceQuery)
        this.deviceTypeInfo = data.deviceTypeInfo
        this.currentLineData = data.deviceTypeSignals
        this.currentLineData.specialFields = []
        this.currentLineData.eventName = data.deviceTypeInfo.vin
        this.isExistDigitalTwin =
          this.currentLineData.defaultFields.length > 0 &&
          this.currentLineData.fullDigitalTwin.length > 0
        // 告警总数
        this.alertTotal = data.alarmCount
        const colorList = [
          '#313CA6',
          '#0082D6',
          '#55D1FD',
          '#3D4A66',
          '#8F9AB2',
          '#B4BBCC',
        ]
        // 告警级别数据
        this.levelList = data.alarmLevel.map((level, i) => {
          return {
            name: this.alertLevel[level.alarmLevel]?.text,
            value: level.count,
            percent: level.format,
            level: level.alarmLevel,
            color: this.alertLevel[level.alarmLevel].color,
          }
        })
        // 告警状态数据
        this.activeList = data.alarmStatus.map((item, j) => {
          return {
            name: this.alarmStatus[item.status]?.text,
            value: item.count,
            color: this.alarmStatus[item.status].color,
            percent: item.format,
            status: item.status,
          }
        })
        //唯一告警类型总数
        // data.uniqueAlarmTypeCount = [
        //   {
        //     assetType: '0',
        //     assetTypeName: 'Vehicle',
        //     count: 1,
        //     format: '100.00%',
        //   },
        // ]
        this.typeList = data.uniqueAlarmTypeCount.map((v, index) => {
          return {
            name: 'Alert Type',
            value: v.count,
            color: colorList[index],
            percent: v.format,
          }
        })
        // this.typeList = Object.assign([], this.assetTypeEnum)
        //   .filter(v => v.value === '0')
        //   .map((item, index) => {
        //     const typeItem = data.uniqueAlarmTypeCount.find(
        //       v => v.assetType == item.value,
        //     )
        //     return {
        //       name: item.text,
        //       value: typeItem?.count,
        //       icon: item.icon.trim(),
        //       color: colorList[index],
        //       percent: typeItem?.format,
        //     }
        //   })

        //告警严重级别趋势alarmTrend
        this.collectionData = data.alarmTrend

        // 安全事件top5
        this.totalEarning = data.safeEvent.map(item => ({
          ...item,
          title: item.name,
          color: this.alertLevel[item.alarmLevel]?.color,
          earning: item.count,
          progress: Math.ceil(item.format.split('%')[0]),
          level: item.alarmLevel,
        }))
        //告警类型分布alarmTypes
        this.alarmTypes = data.alarmTypes.map((item, j) => {
          return {
            name: item.name,
            percent: item.format,
            count: item.count,
            format: Math.ceil(item.format.split('%')[0]),
          }
        })

        //告警严重性分布alarmLevel
        // this.statusList = data.alarmLevel.map((item, i) => {
        //   return {
        //     name: this.alertLevel[item.alarmLevel]?.text,
        //     percent: item.format,
        //     count: item.count,
        //     format: Math.ceil(item.format.split('%')[0]),
        //   }
        // })
        // data.alarmLevel = [
        //   {
        //     alarmLevel: '1',
        //     alarmLevelName: '高',
        //     count: 1,
        //     format: '50.00%',
        //   },
        //   {
        //     alarmLevel: '3',
        //     alarmLevelName: '低',
        //     count: 1,
        //     format: '50%',
        //   },
        // ]
        if (data.alarmLevel.length) {
          this.statusList = Object.assign([], this.alertLevel)
            .filter(v => v.value !== '4' && v.value !== '5')
            .map((item, index) => {
              const typeItem = data.alarmLevel.find(
                v => v.alarmLevel == item.value,
              )
              return {
                name: item.text,
                percent: typeItem ? typeItem.format : '0%',
                count: typeItem ? typeItem.count : 0,
                format: typeItem ? Math.ceil(typeItem.format.split('%')[0]) : 0,
              }
            })
        }
      } catch (err) {
        console.log('IDPS报错', err)
      } finally {
        this.$hideLoading()
        this.isLoading = false
      }
    },
  },
}
</script>
<style lang="scss">
.alert-list ::v-deep.v-avatar > span {
  text-overflow: initial !important;
  overflow: initial !important;
}
.asset-children.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.asset-children.v-data-table
  > .v-data-table__wrapper
  > table
  > tbody
  > tr
  > td {
  padding: 0 !important;
  border-bottom: 0 !important;
}

.asset-children.v-tabs :hover::before,
.asset-children.v-tabs ::before {
  border-radius: 24px;
}
</style>
