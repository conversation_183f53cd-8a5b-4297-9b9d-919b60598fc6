import { num, numberToFormat } from '@/util/filters'
import { hexToRgb } from '@core/utils/index.js'

import { i18n } from '@/plugins/i18n'
import store from '@/store'
import { format, subHours } from 'date-fns'
import * as echarts from 'echarts'
import moment from 'moment'
import Vue from 'vue'
export let backgroundColor = 'transparent'

// 主色调
export let primary = '#44E2FE'

export let getRoundSize = num => {
  let screenWidth = store.state.global.clientWidth
  return Math.round((screenWidth / 1920) * num)
}

// 一般用于image宽高
export let getCeilSize = num => {
  let screenWidth = store.state.global.clientWidth
  return Math.ceil((screenWidth / 1920) * num)
}
Vue.prototype.$getCeilSize = getCeilSize

export let textStyle = () => {
  return {
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: getRoundSize(14),
    // lineHeight: getRoundSize(14),
    // color: '#ffffffcc',
  }
}

export let tooltip = () => {
  return {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
    },
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    textStyle: {
      color: '#ffffff',
      fontSize: getRoundSize(14),
      lineHeight: getRoundSize(22),
    },
    extraCssText: 'backdrop-filter: blur(6px);',
  }
}

export let legend = () => {
  return {
    top: 0,
    right: 0,
    show: true,
    // left: 'center',
    icon: 'circle',
    itemHeight: getRoundSize(8),
    itemGap: getRoundSize(50),
    textStyle: {
      fontSize: getRoundSize(10),
      padding: [0, 0, 0, -8],
      color: '#ffffff99',
    },
  }
}

export let grid = {
  top: '30%',
  left: '1%',
  right: '1%',
  bottom: '0',
  containLabel: true,
}
// 坐标字体
export let axisLabel = () => {
  return {
    fontSize: getRoundSize(12),
  }
}

// 定义一个函数来处理不同格式的时间和日期字符串
const parseAndFormatValue = value => {
  // 尝试匹配 "HH:mm" 格式
  const timePattern = /^(\d{1,2}):(\d{2})$/
  const dateRangePattern = /^(\d{1,2})-(\d{1,2})$/

  if (timePattern.test(value)) {
    // 如果是 "HH:mm" 格式，则返回前一个小时到当前时间的范围
    const [hours, minutes] = value.split(':').map(Number)
    const m = moment({ hours, minutes })

    const start = format(subHours(m.toDate(), 1), 'HH:mm')
    const end = format(m.toDate(), 'HH:mm')
    return start === end ? end : `${start}~${end}`
  } else if (dateRangePattern.test(value)) {
    // 如果是 "MM-DD" 或者 "DD-DD" 格式，则直接返回该值
    return value
  } else {
    // 尝试用更通用的方式解析其他格式
    const m = moment(value, ['YYYY-MM-DD', 'YYYY-MM-DD HH:mm:ss'], true)
    if (m.isValid()) {
      const hasTime = value.includes(' ')
      if (hasTime) {
        const start = format(subHours(m.toDate(), 1), 'HH:mm')
        const end = format(m.toDate(), 'HH:mm')
        return start === end ? end : `${start}~${end}`
      } else {
        return m.format('MM-DD')
      }
    } else {
      // 如果解析失败，返回原始字符串
      return value
    }
  }
}

export let xAxisFn = data => {
  return {
    type: 'category',
    boundaryGap: false,
    data: data,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: axisLabel(),
    axisPointer: {
      show: true,
      label: {
        formatter: ({ value }) => {
          // console.log('formatter value:', value)
          return parseAndFormatValue(value)
        },
      },
    },
  }
}

// 虚线样式
export let splitLine = {
  show: true,
  lineStyle: {
    type: [5, 5],
    dashOffset: 0,
    shadowBlur: 0,
    opacity: 0.15,
    // color: '#28354b',
  },
}

export let yAxisFn = () => {
  return [
    {
      type: 'value',
      splitLine: splitLine,
      axisLabel: axisLabel(),
    },
  ]
}

export let linearSeriesFn = (color, data, name) => {
  let rgbObj = ''
  if (color.includes('#')) {
    rgbObj = hexToRgb(color)
  }

  const startColor =
    `rgba(${rgbObj.r},${rgbObj.g},${rgbObj.b},0.8)` ||
    'rgba(251, 114, 147, 0.6)'
  const endColor =
    `rgba(${rgbObj.r},${rgbObj.g},${rgbObj.b},0)` || 'rgba(251, 114, 147, 0)'
  return {
    name: name,
    type: 'line',
    // stack---面积图堆叠
    // stack: 'Total',
    // 平滑曲线
    // smooth: true,
    color: startColor,
    lineStyle: {
      width: 1,
      type: [5, 5],
      dashOffset: 0,
      shadowBlur: 0,
    },
    showSymbol: true,
    symbol: 'circle',
    symbolSize: 6,

    areaStyle: {
      opacity: 0.8,
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: startColor,
        },
        {
          offset: 1,
          color: endColor,
        },
      ]),
      itemStyle: {
        borderWidth: 50,
      },
    },
    emphasis: {
      focus: 'series',
    },
    data: data,
  }
}

export let linearOptionFn = (xList, yList) => {
  return {
    backgroundColor,
    tooltip: tooltip(),
    legend: {
      ...legend(),
      ...{
        formatter: name => {
          return name
        },
      },
    },
    grid: {
      ...grid,
      right: '4%',
    },
    xAxis: xAxisFn(xList),
    yAxis: yAxisFn(),
    series: yList.map(item => {
      return linearSeriesFn(item.color, item.data, item.name)
    }),
  }
}

export let stackSeriesFn = (color, data, name) => {
  return {
    name: name,
    type: 'bar',
    // stack: 'total-profit',
    barWidth: 12,
    barMinHeight: getRoundSize(8),
    chip: false,
    emphasis: { focus: 'series' },
    itemStyle: {
      borderRadius: 10,
      borderWidth: 5,
      borderColor: 'transparent',
      borderJoin: 'round',
      borderType: 'solid',
    },
    data: data,
    color: color,
  }
}

export let stackOptionFn = (xList, yList) => {
  return {
    backgroundColor,
    tooltip: {
      ...tooltip(),
      trigger: 'axis',
      axisPointer: { type: 'none' },
      borderWidth: 0,
    },
    legend: {
      ...legend(),
      ...{
        formatter: name => {
          return name.slice(0, 1)
        },
      },
    },
    // legend: legend(),
    grid: {
      ...grid,
      right: '4%',
      top: '22%',
    },
    xAxis: xAxisFn(xList),
    yAxis: yAxisFn(),
    series: yList.map(item => {
      return stackSeriesFn(item.color, item.data, item.name)
    }),
  }
}

export let lineSeriesFn = (color, data, name) => {
  return {
    type: 'line',
    name: name,
    data: data,
    color: color,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      type: [5, 5],
      dashOffset: 0,
      shadowBlur: 10,
      width: 3,
      shadowOffsetY: 3,
      shadowColor: color,
    },
  }
}

export let lineSeriesFnForLine = (color, data, name) => {
  return {
    type: 'line',
    name: name,
    data: data,
    color: color,
    symbol: 'circle',
    symbolSize: 10,
    // 加粗线条
    lineStyle: {
      type: [5, 5],
      dashOffset: 0,
      shadowBlur: 10,
      width: 3,
      shadowOffsetY: 3,
      shadowColor: color,
    },
    emphasis: {
      show: true,
      scale: true,
      width: 5,
      shadowBlur: 15,
    },
  }
}

export let lineOptionFn = (xList, yList, updateDate) => {
  return {
    backgroundColor,
    tooltip: {
      ...tooltip(),
      trigger: 'axis',
      axisPointer: { type: 'line' },
      borderWidth: 0,
      formatter: params => {
        let res = ''
        params.forEach((v, index) => {
          if (index === 0) {
            res += `${v.axisValueLabel}`
          }
          res += ` <br> ${v.marker}<span style="margin-left:5px;">${
            v.seriesName
          }</span><span style="margin-left:15px;">${
            v.value === 0.5 ? 0 : num(v.value)
          }</span>`
        })
        return res
      },
    },
    legend: {
      ...legend(),
      ...{
        formatter: name => {
          if (updateDate) {
            return `${i18n.t('global.count')}    ${i18n.t('global.updateDiff', [
              updateDate,
            ])}`
          } else {
            return name
          }
        },
      },
    },
    grid: {
      ...grid,
      right: '4%',
    },
    xAxis: xAxisFn(xList),
    yAxis: {
      type: 'value',
      min: '0.5',
      splitLine: splitLine,
      axisLabel: {
        showMinLabel: false,
        fontSize: getRoundSize(12),
        formatter(val) {
          return numberToFormat(val)
        },
      },
    },
    series: yList.map(item => {
      return lineSeriesFnForLine(item.color, item.data, item.name)
    }),
  }
}

export const cloudTooltip = {
  trigger: 'item',
  formatter: '{b}:\t{c}({d}%)',
  backgroundColor: '',
  padding: [6, 10],
  textStyle: { color: '#fff' },
}

export const cloudPieSeriesFn = (list, formatter) => {
  return [
    {
      name: '',
      type: 'pie',
      radius: ['46%', '57%'],
      avoidLabelOverlap: true,
      percentPrecision: 0,
      minAngle: 20,
      stillShowZeroSum: true,
      top: -getRoundSize(70),
      bottom: 0,
      left: 0,
      label: {
        show: true,
        position: 'center',
        // lineHeight: getRoundSize(0),
        formatter: formatter,
        rich: {
          name: {
            fontSize: getRoundSize(12),
            color: '#fff',
            opacity: 0.6,
            lineHeight: getRoundSize(14),
          },
          value: {
            lineHeight: getRoundSize(28),
            fontSize: getRoundSize(24),
            fontWeight: 600,
            padding: [getRoundSize(12), 0, 0, 0],
            // verticalAlign: 'bottom',
            color: primary,
          },
          unit: {
            fontSize: getRoundSize(14),
            fontWeight: 500,
            padding: [getRoundSize(16), 0, 0, 0],
            // padding: [getRoundSize(10), getRoundSize(5)],
            // verticalAlign: 'bottom',
            color: primary,
            opacity: 0.6,
          },
        },
      },
      data: list,
      itemStyle: { borderWidth: 0, borderColor: '#fff' },
      emphasis: {
        show: true,
        scale: true,
      },
    },
  ]
}

export const cloudPieLegendFn = list => {
  return {
    // right: getRoundSize(10),
    // top: '0',
    bottom: getRoundSize(14),
    icon: 'circle',
    itemGap: getRoundSize(14), // 图标间距
    width: '44%',
    itemHeight: getRoundSize(10),
    textStyle: {
      fontSize: getRoundSize(12),
      // padding: [0, 0, 0, 0],
      color: '#fff',
      opacity: 0.6,
      // width: getRoundSize(100),
      overflow: 'truncate',
    },
    formatter: name => {
      if (list && list.length) {
        const cur = list.find(v => v.name === name)
        return `{name|${name} \t ${cur.value}}{value|}Gbps`
      } else {
        return `{name|${name}`
      }
    },
  }
}

// export const cloudPieColor = ['#4A687B', '#177754', '#4CA3B6', '#1B3F82']
export const cloudPieColor = ['#21CBFF', '#32FDB8', '#F0DA4C']
