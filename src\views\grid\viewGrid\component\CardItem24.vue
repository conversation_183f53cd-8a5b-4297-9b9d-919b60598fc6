<template>
  <!-- 堆叠柱状图 -->
  <!-- <div class="box position-relative"></div> -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <!-- style="position: absolute !important; top: 10%" -->
    <template v-if="list.length !== 0">
      <vsoc-chart
        :echartId="echartId"
        class="box-chart d-flex align-center"
        :option="option"
      ></vsoc-chart>
    </template>

    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { num, numberToFormat } from '@/util/filters'
import {
  axisLabel,
  getRoundSize,
  grid,
  legend,
  splitLine,
  stackSeriesFn,
  tooltip,
} from './chart'
export default {
  name: 'CardItem24',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    echartId: {
      type: String,
      default: '',
    },
  },
  components: {
    VsocChart,
  },
  data() {
    return {}
  },
  computed: {
    option() {
      const baseSeries = {
        type: 'bar',
        stack: 'total-profit',
        barWidth: 12,
        barMinHeight: getRoundSize(4),
        chip: false,
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          // borderRadius: 10,
          // borderWidth: 5,
          borderColor: 'transparent',
          // borderJoin: 'round',
          // borderType: 'solid',
        },
      }
      let yObj = this.list[0].arrays.map(v => {
        return {
          name: v.name,
          stateOwnedCount: 0,
        }
      })

      yObj.forEach(v => {
        this.list.forEach(item => {
          const stateOwned = item.arrays.find(entry => entry.name === v.name)
          if (stateOwned) {
            v.stateOwnedCount += stateOwned.number
          }
        })
      })

      let yList = []
      yList = yObj
        .sort((a, b) => a.stateOwnedCount - b.stateOwnedCount)
        .map(v => v.name)

      let xList = this.list.sort((a, b) => Number(a.type) - Number(b.type))

      let _this = this
      return {
        backgroundColor: 'transparent',
        tooltip: {
          ...tooltip(),
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          borderWidth: 0,
          // formatter: function (value) {
          //   if (_this.echartId === 'C056') {
          //     const percent1 = _this.list[0].arrays.find(
          //       v => v.xName === value[0].name,
          //     )?.percent
          //     const percent2 = _this.list[1].arrays.find(
          //       v => v.xName === value[1].name,
          //     )?.percent
          //     return `${value[0].marker}  ${value[0].seriesName}：${num(
          //       value[0].value,
          //     )} (${percent1}%) <br/> ${value[1].marker}  ${
          //       value[1].seriesName
          //     }：${num(value[1].value)} (${percent2}%)`
          //   }
          // },
        },
        legend: {
          ...legend(),
          top: '2%',
        },
        grid: {
          ...grid,
          right: '3%',
          top: '17%',
        },
        xAxis: {
          type: 'value',
          // splitLine: splitLine,
          splitLine: {
            show: false,
          },
          axisLabel: {
            ...axisLabel(),
            color: '#fff',
            opacity: 0.7,
            formatter: function (value) {
              return numberToFormat(value)
            },
          },
        },
        yAxis: {
          type: 'category',
          boundaryGap: false,
          data: yList,
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            ...axisLabel(),
            color: '#fff',
            opacity: 0.7,
          },
          axisPointer: {
            show: true,
            label: {
              formatter: value => {
                // return `${value.value}`
              },
            },
          },
        },

        series: xList.map(item => {
          return {
            ...baseSeries,
            name: item.name,
            color: _this.$store.state.enums.enums.AlarmLevel[item.type]?.color,
            data: item.arrays
              .sort((a, b) => a.number - b.number)
              .map(s => {
                return s.number
              }),
          }
        }),
      }
    },
  },
}
</script>
