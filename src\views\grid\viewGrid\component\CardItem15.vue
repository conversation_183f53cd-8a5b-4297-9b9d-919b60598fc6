<template>
  <!-- 条形图 上下布局 -->
  <div class="box">
    <div
      :ref="'ref' + itemKey"
      class="position-relative big-content-box box-line-3"
    >
      <div ref="boxHeader" class="box-header">
        {{ title }}
        <!-- <span v-if="itemKey === 'C053'" class="box-header-num">{{
          numberToFormat(allTotal)
        }}</span> -->
      </div>
      <!-- C002安徽省网联车辆销售排名TOP10 -->
      <!-- <img
        v-if="itemKey === 'C002'"
        class="position-absolute top-image"
        src="../images/top11.svg"
      /> -->

      <div
        v-if="['C002', 'C007'].includes(itemKey)"
        class="position-absolute top-tab d-flex align-center"
      >
        <div
          v-for="(item, index) in titleList"
          :key="index"
          :class="[
            'h-100',
            'cursor-pointer',
            'font-weight-semibold',
            'color--primary',
            titleValue === item.value ? 'top-tab-text' : '',
            index === 0 ? 'top-tab-1' : '',
          ]"
          @click.stop="changeTab(item)"
        >
          {{ item.text }}
        </div>
      </div>

      <div class="tab-content tab-content-1 mt-0 h-100">
        <template v-if="list.length !== 0">
          <vue-seamless-scroll
            ref="seamlessScroll"
            :data="list"
            :class-option="classOption"
            style="overflow: hidden"
            class="h-100"
          >
            <div ref="boxContent">
              <div
                class="d-flex align-center w-100 list-item"
                v-for="(item, index) in list"
                :key="index"
              >
                <div
                  v-if="['C010', 'C002'].includes(itemKey)"
                  class="dot-1 text-center"
                  :style="{
                    background: getTopColor(index + 1, itemKey),
                  }"
                >
                  {{ index + 1 }}
                </div>
                <!-- C007车企备案平台信息 -->
                <div
                  v-else
                  class="dot align-start"
                  :style="{
                    background:
                      itemKey === 'C007'
                        ? getClassificationColor(item.classificationLevel)
                        : '#44e2fe',
                  }"
                ></div>
                <div
                  class="flex-1 ml-4"
                  :class="itemKey === 'C010' ? 'cursor-pointer' : ''"
                  @click.stop="itemKey === 'C010' ? jumpPage(item) : ''"
                >
                  <div class="d-flex justify-space-between align-start">
                    <div>
                      <div class="risk-name fs-14-1 d-flex align-center">
                        <div>
                          {{
                            itemKey === 'C007'
                              ? item.platformName
                              : item.automakerName
                          }}
                        </div>
                        <div v-if="itemKey === 'C010'" class="cardItem-15 ml-2">
                          <vsoc-icon
                            type="fill"
                            icon="icon-tiaozhuan"
                          ></vsoc-icon>
                        </div>
                      </div>
                      <!-- <div v-if="itemKey === 'C053'" class="fs-12">
                      {{ item.riskSafetyIndex }}%
                    </div> -->
                    </div>
                    <div class="color--primary fs-14">
                      {{
                        itemKey === 'C007'
                          ? item.platformCode
                          : numberToFormat(item.riskScore)
                      }}
                    </div>
                  </div>
                  <!-- 进度条 -->
                  <v-progress-linear
                    background-color="transparent"
                    :color="
                      itemKey === 'C007'
                        ? getClassificationColor(item.classificationLevel)
                        : itemKey === 'C002'
                        ? '#44e2fe'
                        : '#da1f1f'
                    "
                    :value="itemKey === 'C007' ? 100 : item.riskPercentage"
                    class="progress-linear progress-linear-15"
                  >
                  </v-progress-linear>
                </div>
              </div>
            </div>
          </vue-seamless-scroll>
        </template>
        <template v-else>
          <div class="h-full">
            <div
              class="fs-16 color--primary h-100 d-flex justify-center align-center"
            >
              None
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { getClassificationColor, getTopColor } from '../util/defaultPreview'
import { getAutomakerSecurityLevelUnified } from '@/util/algorithm'
import {
  canvasDetail,
  getCanvas,
  layoutItemData,
  validLayoutItems,
} from '@/api/grid/index'
export default {
  name: 'CardItem15',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    total: {
      type: [Number, String],
      default: 0,
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    itemKey: {
      type: String,
      default: '',
    },
  },
  created() {},
  data() {
    return {
      numberToFormat,
      classOption: {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 10000,
        hoverStop: true, // 是否开启鼠标悬停stop
      },
      titleValue: this.itemKey,
      titleList: [
        {
          text: '平台',
          value: 'C002',
        },
        {
          text: '车企',
          value: 'C007',
        },
      ],
      // title: this.title,
      // list: this.list,
      // itemKey: this.itemKey,
    }
  },
  computed: {
    allTotal() {
      return this.list.reduce((acc, item) => {
        return acc + (item.riskSafetyCount || 0)
      }, 0)
    },
  },
  components: {
    vueSeamlessScroll,
  },
  mounted() {
    this.getBoxHeight()
    window.addEventListener('resize', this.getBoxHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getBoxHeight)
  },
  methods: {
    changeTab(item) {
      this.titleValue = item.value
      this.$emit('changeTab', item.value)
      // let findItem = this.layoutData.find(v => v.itemKey === item.value)
      // let list = []
      // let title = ''
      // let itemKey = ''
      // if (findItem) {
      //   list = findItem.value
      //   title = findItem.itemName
      //   itemKey = findItem.itemKey
      // }
      // this.$refs.seamlessScroll && this.$refs.seamlessScroll.reset()
      // this.classOption.limitMoveNum = 10000
      // this.list = list
      // this.title = title
      // this.itemKey = itemKey
      // this.getBoxHeight()
    },
    // 获取定级级别颜色
    getClassificationColor,
    getTopColor,
    getLevelColor(item) {
      return getAutomakerSecurityLevelUnified({
        riskDetails: item.riskDetails,
        riskScore: item.riskScore,
      })
    },
    async jumpPage(item) {
      try {
        const res = await getCanvas({ name: '' })
        let findItem = res.data.find(v => v.backGroundImg === '3')
        if (findItem) {
          this.$router.push(
            `/grid/shareCanvas?id=${findItem.id}&currentModelType=${item.automakerCode}`,
          )
        }
      } catch (e) {
        console.error(`获取画布管理管理：${e}`)
      }
    },
    getBoxHeight() {
      setTimeout(() => {
        let boxLineHeight =
          this.$refs['ref' + this.itemKey]?.offsetHeight -
          this.$refs.boxHeader.offsetHeight -
          8
        let boxContentHeight = this.$refs.boxContent?.offsetHeight

        this.$refs.seamlessScroll && this.$refs.seamlessScroll.reset()
        if (boxLineHeight < boxContentHeight) {
          this.classOption.limitMoveNum = this.list.length
        } else {
          this.classOption.limitMoveNum = 10000
        }
      }, 300)
    },
  },
}
</script>
<style lang="scss">
// .data-view-1 .progress-linear-15 .v-progress-linear__determinate {
//   border-color: linear-gradient(
//     92.38deg,
//     #0f3480 0.9%,
//     #224fa9 99.43%
//   ) !important;
//   background: linear-gradient(
//     92.38deg,
//     #0f3480 0.9%,
//     #224fa9 99.43%
//   ) !important;
// }
</style>
