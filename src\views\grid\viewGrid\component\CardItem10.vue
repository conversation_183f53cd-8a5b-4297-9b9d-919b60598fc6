<template>
  <!-- 环形图2（带线）资产态势 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <template v-if="list.length !== 0">
      <vsoc-chart
        class="box-chart d-flex align-center"
        :echartId="echartId"
        :option="chartOption"
        @highlight="onHighlight"
      ></vsoc-chart>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
    <!-- <vsoc-chart
      ref="echart"
      class="box-chart d-flex align-center"
      :echartId="echartId"
      :option="chartOption"
      @highlight="onHighlight"
    ></vsoc-chart> -->
  </div>
</template>
<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'

import { getRoundSize, tooltip } from './chart'
import { getClassificationColor } from '../util/defaultPreview'
export default {
  name: 'CardItem10',
  components: {
    VsocChart,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    total: {
      type: [Number, String],
      default: () => {
        return 0
      },
    },
    echartId: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      // list1: [
      //   { name: '不是问题', value: 831 },
      //   { name: '有效事件', value: 1373 },
      //   { name: '处理中', value: 72 },
      //   { name: '待处理', value: 578 },
      //   { name: '重复', value: 35 },
      //   { name: '误报', value: 22 },
      // ],
    }
  },
  computed: {
    alarmLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    chartOption() {
      let list = []
      this.list.forEach((item, index) => {
        list.push({
          name: item.name,
          value: item.value,
          percentage: item.percentage,
          type: item.type || index,
        })
      })
      let total = this.list.reduce((accumulator, currentValue) => {
        return accumulator + currentValue.value
      }, 0) // 初始值为0
      // let list = [
      //   {
      //     name: 'Poor (D)',
      //     type: '3',
      //     value: 8527,
      //     percentage: '18.23%',
      //   },
      //   {
      //     name: 'Strong (A)',
      //     type: '0',
      //     value: 37534,
      //     percentage: '80.23%',
      //   },
      //   {
      //     name: 'Fair (C)',
      //     type: '2',
      //     value: 621,
      //     percentage: '1.33%',
      //   },
      //   {
      //     name: 'Good (B)',
      //     type: '1',
      //     value: 100,
      //     percentage: '0.21%',
      //   },
      // ]
      const common = {
        type: 'pie',
        clockwise: false,
        emphasis: {
          scale: true,
          scaleSize: 2,
        },
        center: ['50%', '52%'],
        bottom: -getRoundSize(14),
        percentPrecision: 2,
        // center: ['50%', '50%'],
        // width: '40%',
        // left: 'center',
        // top: getRoundSize(30),
        // bottom: getRoundSize(20),
        // percentPrecision: 0,
      }
      const rich = {
        name: {
          width: getRoundSize(110),
          fontSize: getRoundSize(14),
          padding: [0, getRoundSize(20), 0, 0],
        },
        value: {
          padding: [0, 0, 0, 0],
          width: getRoundSize(0),
        },
        percentage: {
          padding: [0, 0, 0, 0],
          width: getRoundSize(0),
        },
      }
      const _this = this
      const option = {
        tooltip: {
          ...tooltip(),
          trigger: 'item',
          formatter(val) {
            return (
              val.marker +
              '\t' +
              val.name +
              ':\t' +
              val.value +
              '(' +
              val.percent +
              '%)'
            )
          },
        },
        backgroundColor: 'transparent',
        // color: chroma
        //   .scale(['#32FDB8', '#21CBFF', '#FF9229', '#FF385D'])
        //   .mode('lch')
        //   .colors(list.length),

        legend: {
          show: false,
        },

        // textStyle: {
        //   fontStyle: 'normal',
        //   fontWeight: 400,
        //   color: '#ffffcc',
        // },

        title: {
          text: numberToFormat(total),
          position: 'center',
          top: '49%',
          left: 'center',
          // bottom: 'center',
          textStyle: {
            // lineHeight: getRoundSize(60),
            fontSize: getRoundSize(18),
            fontWeight: 600,
            // padding: [getRoundSize(108), 0, getRoundSize(8), getRoundSize(8)],
            // verticalAlign: 'right',
            color: '#fff',
          },
        },

        series: [
          // {
          //   name: '里面的环',
          //   ...common,
          //   radius: ['48%', '55%'],
          //   emphasis: { scale: false },
          //   color: '#000',
          //   label: { show: false },
          //   tooltip: { show: false },
          //   data: [{ value: 100, name: '', percentage: '' }],
          // },
          {
            // name: '中间的环',
            ...common,
            // percentPrecision: 0,
            radius: this.isFull ? ['50%', '61%'] : ['40%', '51%'],
            // emphasis: { scale: true },
            data: list,
            // labelLayout: {
            //   hideOverlap: false,
            // },
            itemStyle: {
              normal: {
                opacity: 1,
                color: function (params) {
                  if (_this.echartId === 'C028') {
                    return (
                      _this.alarmLevel[Number(params.data.type)]?.color ||
                      '#32FDB8'
                    )
                  } else if (_this.echartId === 'C008') {
                    return getClassificationColor(params.data.type) || '#32FDB8'
                  } else if (_this.echartId === 'C024') {
                    var colors = ['#32fdb8', '#21cbff']
                    return colors[Number(params.data.type)]
                  }
                },
              },
            },
            // avoidLabelOverlap: true,
            // minAngle: 5, //最小角度
            // startAngle: 100, //起始角度
            label: {
              normal: {
                position: 'outside',
                alignTo: 'edge',
                edgeDistance: '10%',
                formatter: function (params) {
                  if (params.name !== '') {
                    return `{legend|●}\t{name|${
                      params.name
                    }}\n{value|${numberToFormat(params.value)}\t(${
                      params.percent
                    }%)}`
                  } else {
                    return ''
                  }
                },
                rich: {
                  value: {
                    fontSize: getRoundSize(12),
                    lineHeight: getRoundSize(22),
                    // fontFamily: 'PingFang HK',
                    color: '#FFFFFF',
                    opacity: 0.8,
                    padding: [getRoundSize(8), 0],
                  },
                  name: {
                    fontSize: getRoundSize(12),
                    lineHeight: getRoundSize(22),
                    // fontFamily: 'PingFang HK',
                    color: '#FFFFFF',
                    opacity: 0.8,
                  },
                  legend: {
                    padding: [0, getRoundSize(2), 0, 0],
                    fontSize: getRoundSize(14),
                    color: 'inherit',
                  },
                },
                // formatter: '{hr|}\t {b|{b}}\n{c|{c}}\t {d|{d}%}',
                // rich: {
                //   hr: {
                //     backgroundColor: 'auto',
                //     height: getRoundSize(6),
                //     width: getRoundSize(6),
                //     borderRadius: getRoundSize(3),
                //   },
                //   b: {
                //     fontSize: getRoundSize(14),
                //     // padding: [getRoundSize(16), 0, getRoundSize(10), 0],
                //     lineHeight: getRoundSize(22),
                //     color: '#fff',
                //   },
                //   c: {
                //     fontSize: getRoundSize(14),
                //     color: 'rgba(255, 255, 255, 0.8)',
                //     // lineHeight: getRoundSize(24),
                //     // padding: [0, 0, getRoundSize(16), 0],
                //   },
                //   d: {
                //     fontSize: getRoundSize(14),
                //     color: 'rgba(255, 255, 255, 0.8)',
                //     // lineHeight: getRoundSize(24),
                //     // padding: [0, 0, getRoundSize(16), 0],
                //   },
                // },
              },
            },
            labelLine: {
              length: 8,
              length2: 5,
              lineStyle: {
                width: 1,
              },
            },
          },
          // {
          //   name: '外面的环',
          //   ...common,
          //   percentPrecision: 0,
          //   radius: ['98%', '100%'],
          //   emphasis: { scale: false },
          //   data: [
          //     {
          //       name: '',
          //       value: 50,
          //       percentage: '',
          //       itemStyle: {
          //         color: '#44E2FE',
          //       },
          //     },
          //     {
          //       name: '',
          //       value: 5,
          //       percentage: '',
          //       itemStyle: {
          //         color: 'transparent',
          //       },
          //     },
          //     {
          //       name: '',
          //       value: 50,
          //       percentage: '',
          //       itemStyle: {
          //         color: '#44E2FE',
          //       },
          //     },
          //     {
          //       name: '',
          //       value: 5,
          //       percentage: '',
          //       itemStyle: {
          //         color: 'transparent',
          //       },
          //     },
          //     {
          //       name: '',
          //       value: 50,
          //       percentage: '',
          //       itemStyle: {
          //         color: '#44E2FE',
          //       },
          //     },
          //     {
          //       name: '',
          //       value: 5,
          //       percentage: '',
          //       itemStyle: {
          //         color: 'transparent',
          //       },
          //     },
          //     {
          //       name: '',
          //       value: 50,
          //       percentage: '',
          //       itemStyle: {
          //         color: '#44E2FE',
          //       },
          //     },
          //     {
          //       name: '',
          //       value: 5,
          //       percentage: '',
          //       itemStyle: {
          //         color: 'transparent',
          //       },
          //     },
          //   ],
          //   tooltip: { show: false },
          //   label: { show: false },
          // },
        ],
      }
      return option
    },
  },
  created() {},
  methods: {
    onHighlight(obj, myChart) {
      // const option = this.chartOption
      // // option.tooltip = tooltip()
      // // option.tooltip.trigger = 'item'
      // // option.tooltip.padding = [6, 10]
      // // // option.tooltip.textStyle.color = obj.color
      // // // option.tooltip.backgroundColor = 'rgba(255, 255, 255, 0.1)'
      // // // option.tooltip.textStyle.color = obj.color
      // // console.log(option, 11)
      // myChart.setOption(option)
    },
  },
}
</script>
