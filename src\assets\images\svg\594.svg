<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>GF</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#2DA446" offset="0%"></stop>
            <stop stop-color="#218736" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFE24A" offset="0%"></stop>
            <stop stop-color="#FCDC34" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#E7242C" offset="0%"></stop>
            <stop stop-color="#D61C24" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="GF">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <polygon id="Rectangle-2" fill="url(#linearGradient-3)" points="0 0 21 15 0 15"></polygon>
            <polygon id="Star-53" fill="url(#linearGradient-4)" points="10.5 9.07346292 8.66317109 10.4031781 9.36019403 8.24534541 7.52794839 6.90932189 9.79556117 6.90542313 10.5 4.75 11.2044388 6.90542313 13.4720516 6.90932189 11.639806 8.24534541 12.3368289 10.4031781"></polygon>
        </g>
    </g>
</svg>