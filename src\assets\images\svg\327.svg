<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>KZ</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#30C6E0" offset="0%"></stop>
            <stop stop-color="#1CB0C9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFCD4B" offset="0%"></stop>
            <stop stop-color="#FEC531" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="KZ">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Mask-Copy" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="15"></rect>
            <circle id="Oval-9" fill="url(#linearGradient-3)" cx="10.5" cy="7.5" r="3"></circle>
            <path d="M10.5,11.0352098 L9.27613486,12.8621035 L8.96612997,10.6851139 L7.07080609,11.8000732 L7.73606171,9.70416723 L5.54467123,9.88636057 L7.05342533,8.28665818 L5,7.5 L7.05342533,6.71334182 L5.54467123,5.11363943 L7.73606171,5.29583277 L7.07080609,3.19992685 L8.96612997,4.31488606 L9.27613486,2.13789648 L10.5,3.96479024 L11.7238651,2.13789648 L12.03387,4.31488606 L13.9291939,3.19992685 L13.2639383,5.29583277 L15.4553288,5.11363943 L13.9465747,6.71334182 L16,7.5 L13.9465747,8.28665818 L15.4553288,9.88636057 L13.2639383,9.70416723 L13.9291939,11.8000732 L12.03387,10.6851139 L11.7238651,12.8621035 L10.5,11.0352098 Z M10.5,11 C12.4329966,11 14,9.43299662 14,7.5 C14,5.56700338 12.4329966,4 10.5,4 C8.56700338,4 7,5.56700338 7,7.5 C7,9.43299662 8.56700338,11 10.5,11 Z" id="Star-28" fill="url(#linearGradient-3)"></path>
            <path d="M1,1.25234222 C1,1.11297746 1.10701752,1 1.25234222,1 L1.74765778,1 C1.88702254,1 2,1.10701752 2,1.25234222 L2,1.74765778 C2,1.88702254 1.89298248,2 1.74765778,2 L1.25234222,2 C1.11297746,2 1,1.89298248 1,1.74765778 L1,1.25234222 Z M1,3.25234222 C1,3.11297746 1.10701752,3 1.25234222,3 L1.74765778,3 C1.88702254,3 2,3.10701752 2,3.25234222 L2,3.74765778 C2,3.88702254 1.89298248,4 1.74765778,4 L1.25234222,4 C1.11297746,4 1,3.89298248 1,3.74765778 L1,3.25234222 Z M1,5.25234222 C1,5.11297746 1.10701752,5 1.25234222,5 L1.74765778,5 C1.88702254,5 2,5.10701752 2,5.25234222 L2,5.74765778 C2,5.88702254 1.89298248,6 1.74765778,6 L1.25234222,6 C1.11297746,6 1,5.89298248 1,5.74765778 L1,5.25234222 Z M1,7.25234222 C1,7.11297746 1.10701752,7 1.25234222,7 L1.74765778,7 C1.88702254,7 2,7.10701752 2,7.25234222 L2,7.74765778 C2,7.88702254 1.89298248,8 1.74765778,8 L1.25234222,8 C1.11297746,8 1,7.89298248 1,7.74765778 L1,7.25234222 Z M1,9.25234222 C1,9.11297746 1.10701752,9 1.25234222,9 L1.74765778,9 C1.88702254,9 2,9.10701752 2,9.25234222 L2,9.74765778 C2,9.88702254 1.89298248,10 1.74765778,10 L1.25234222,10 C1.11297746,10 1,9.89298248 1,9.74765778 L1,9.25234222 Z M1,11.2523422 C1,11.1129775 1.10701752,11 1.25234222,11 L1.74765778,11 C1.88702254,11 2,11.1070175 2,11.2523422 L2,11.7476578 C2,11.8870225 1.89298248,12 1.74765778,12 L1.25234222,12 C1.11297746,12 1,11.8929825 1,11.7476578 L1,11.2523422 Z M1,13.2523422 C1,13.1129775 1.10701752,13 1.25234222,13 L1.74765778,13 C1.88702254,13 2,13.1070175 2,13.2523422 L2,13.7476578 C2,13.8870225 1.89298248,14 1.74765778,14 L1.25234222,14 C1.11297746,14 1,13.8929825 1,13.7476578 L1,13.2523422 Z M2,12.2523422 C2,12.1129775 2.10701752,12 2.25234222,12 L2.74765778,12 C2.88702254,12 3,12.1070175 3,12.2523422 L3,12.7476578 C3,12.8870225 2.89298248,13 2.74765778,13 L2.25234222,13 C2.11297746,13 2,12.8929825 2,12.7476578 L2,12.2523422 Z M2,10.2523422 C2,10.1129775 2.10701752,10 2.25234222,10 L2.74765778,10 C2.88702254,10 3,10.1070175 3,10.2523422 L3,10.7476578 C3,10.8870225 2.89298248,11 2.74765778,11 L2.25234222,11 C2.11297746,11 2,10.8929825 2,10.7476578 L2,10.2523422 Z M2,8.25234222 C2,8.11297746 2.10701752,8 2.25234222,8 L2.74765778,8 C2.88702254,8 3,8.10701752 3,8.25234222 L3,8.74765778 C3,8.88702254 2.89298248,9 2.74765778,9 L2.25234222,9 C2.11297746,9 2,8.89298248 2,8.74765778 L2,8.25234222 Z M2,6.25234222 C2,6.11297746 2.10701752,6 2.25234222,6 L2.74765778,6 C2.88702254,6 3,6.10701752 3,6.25234222 L3,6.74765778 C3,6.88702254 2.89298248,7 2.74765778,7 L2.25234222,7 C2.11297746,7 2,6.89298248 2,6.74765778 L2,6.25234222 Z M2,4.25234222 C2,4.11297746 2.10701752,4 2.25234222,4 L2.74765778,4 C2.88702254,4 3,4.10701752 3,4.25234222 L3,4.74765778 C3,4.88702254 2.89298248,5 2.74765778,5 L2.25234222,5 C2.11297746,5 2,4.89298248 2,4.74765778 L2,4.25234222 Z M2,2.25234222 C2,2.11297746 2.10701752,2 2.25234222,2 L2.74765778,2 C2.88702254,2 3,2.10701752 3,2.25234222 L3,2.74765778 C3,2.88702254 2.89298248,3 2.74765778,3 L2.25234222,3 C2.11297746,3 2,2.89298248 2,2.74765778 L2,2.25234222 Z" id="Rectangle-294" fill="url(#linearGradient-3)"></path>
        </g>
    </g>
</svg>