import { request, vsocPath } from '../../util/request'

// 车企平台分页列表查询
export const getAutomakerPlatformList = function (data) {
  return request({
    url: `${vsocPath}/platform/platforms`,
    method: 'post',
    loading: true,
    data: {
      platformName: data.platform_name || '',
      isSupervise: data.is_supervise || '',
      automakerId: data.automaker_id || '',
      pageNum: data.pageNum || 1,
      pageSize: data.pageSize || 10,
    },
  })
}

// 新增车企平台
export const addAutomakerPlatform = function (data) {
  return request({
    url: `${vsocPath}/platform/addPlatform`,
    method: 'post',
    data: {
      platformName: data.platform_name,
      platformCode: data.platform_code,
      registrationNumber: data.registration_number,
      description: data.description,
      platformTypes: data.platform_type || [], // 注意：接口文档中是platformTypes数组
      //pictureCode: data.picture_code,
      isSupervise: data.is_supervise,
      classificationLevel: data.classification_level,
      automakerId: data.automaker_id,
    },
  })
}

// 更新车企平台
export const editAutomakerPlatform = function (data) {
  return request({
    url: `${vsocPath}/platform/updatePlatform`,
    method: 'post',
    data: {
      id: data.id,
      platformName: data.platform_name,
      platformCode: data.platform_code,
      registrationNumber: data.registration_number,
      description: data.description,
      platformTypes: data.platform_type || [], // 注意：接口文档中是platformTypes数组
      //pictureCode: data.picture_code,
      isSupervise: data.is_supervise,
      classificationLevel: data.classification_level,
      automakerId: data.automaker_id,
    },
  })
}

// 获取车企平台详情
export const getAutomakerPlatformDetail = function (data) {
  return request({
    url: `${vsocPath}/platform/platformDetails`,
    method: 'post',
    data: {
      id: data.id,
    },
  })
}

// 删除车企平台
export const deleteAutomakerPlatform = function (data) {
  return request({
    url: `${vsocPath}/platform/deletePlatform`,
    method: 'post',
    data: {
      id: data.id,
    },
  })
}

// 获取车企列表（用于下拉选择）
export const getAutomakerListForSelect = function () {
  return request({
    url: `${vsocPath}/automaker/automakerList`,
    method: 'post',
    loading: true,
    data: {
      automakerName: '',
      isSupervise: '',
    },
  })
}
