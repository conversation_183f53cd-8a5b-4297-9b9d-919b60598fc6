<template>
  <!-- 定制化图形4-->
  <div class="box box-chart">
    <div :ref="'ref' + itemKey" class="box pa-0">
      <div ref="boxHeader" class="box-header">{{ title }}</div>
      <template v-if="list1.length">
        <v-data-table
          fixed-header
          class="table-card table-card-1 mt-2"
          dense
          style="background: transparent !important"
          :items-per-page="list.length"
          hide-default-footer
          :headers="headers1.length ? headers1 : headers"
          item-key="index"
          :items="list1"
        >
          <template v-slot:item.vehicleCompanyName="{ item }">
            <div
              v-if="item.vehicleCompanyName"
              v-show-tips="item.vehicleCompanyName"
              style="max-width: 16rem"
            >
              {{ item.vehicleCompanyName }}
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.name="{ item }">
            <div
              v-if="item.name && itemKey !== 'C018'"
              class="d-flex align-center"
            >
              <div
                v-if="item.alarmLevel && alertLevel[Number(item.alarmLevel)]"
                class="d-flex align-center"
              >
                <vsoc-icon
                  type="fill"
                  icon="icon-gaojingjibiebiaozhi"
                  size="1.2rem"
                  :style="{
                    color: alertLevel[Number(item.alarmLevel)].color,
                  }"
                ></vsoc-icon>
              </div>
              <div
                v-show-tips="item.name"
                class="my-2"
                style="max-width: 14rem; padding-left: 2px; font-weight: 600"
              >
                {{ item.name }}
              </div>
            </div>
            <div
              v-else-if="item.name && itemKey === 'C018'"
              class="d-flex align-center"
            >
              <div
                v-if="item.type && item.type > 0"
                class="d-flex align-center"
              >
                <v-img
                  :src="getCountryImage(item.type)"
                  class="my-2 mr-2"
                ></v-img>
              </div>
              <div
                v-show-tips="item.name"
                style="max-width: 14rem; font-weight: 600"
              >
                {{ item.name }}
              </div>
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.alarmLevel="{ item }">
            <div
              v-if="item.alarmLevel && alertLevel[Number(item.alarmLevel)]"
              class="d-flex align-center"
            >
              <vsoc-icon
                type="fill"
                icon="icon-gaojingjibiebiaozhi"
                size="1.2rem"
                :style="{ color: alertLevel[Number(item.alarmLevel)].color }"
                class="mr-2"
              ></vsoc-icon>
              <span>{{ alertLevel[Number(item.alarmLevel)].text }}</span>
            </div>
            <div v-else>N/A</div>
          </template>

          <template v-slot:item.alarmDate="{ item }">
            <div>{{ item.alarmDate | toDate }}</div>
          </template>

          <!-- Henry -->

          <template v-slot:item.value="{ item }">
            <div>{{ numberToFormat(item.value) }} ({{ item.percentage }})</div>
          </template>

          <template v-slot:item.status="{ item }">
            <div v-if="alertStatus[item.status]">
              <v-badge
                dot
                inline
                offset-x="10"
                :offset-y="-18"
                :color="alertStatus[item.status].color"
                class="mr-1"
              ></v-badge>
              <span>{{ alertStatus[item.status].text }}</span>
            </div>
            <span v-else>N/A</span>
          </template>

          <template v-slot:item.topNum="{ item, index }">
            <div
              :style="{ background: getTopColor(index + 1, itemKey) }"
              class="risk-num-sort"
            >
              {{ index + 1 }}
            </div>
          </template>

          <template v-slot:item.riskStatus="{ item }">
            <div
              v-if="item.statusName"
              :style="{ color: riskColor[item.riskStatus] }"
            >
              {{ item.statusName }}
            </div>
            <span v-else>/</span>
          </template>
          <template v-slot:item.riskRemark="{ item }">
            <v-btn class="risk-small-btn" small icon @click.stop="">
              <vsoc-icon
                type="fill"
                size="large"
                class="color--primary"
                icon="icon-shenheren1"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>

        <vue-seamless-scroll
          ref="seamlessScroll"
          :data="list1"
          :class-option="classOption"
          style="overflow: hidden"
        >
          <v-data-table
            fixed-header
            class="table-card table-card-2 flex-1"
            dense
            :style="{ overflow: isFull ? 'auto' : '' }"
            style="background: transparent !important"
            :items-per-page="list.length"
            hide-default-footer
            :headers="headers1.length ? headers1 : headers"
            item-key="index"
            :items="list1"
          >
            <template v-slot:item.vehicleCompanyName="{ item }">
              <div
                v-if="item.vehicleCompanyName"
                v-show-tips="item.vehicleCompanyName"
                style="max-width: 16rem"
              >
                {{ item.vehicleCompanyName }}
              </div>
              <div v-else>N/A</div>
            </template>

            <template v-slot:item.name="{ item }">
              <div
                v-if="item.name && itemKey !== 'C018'"
                class="d-flex align-center"
              >
                <div
                  v-if="item.alarmLevel && alertLevel[Number(item.alarmLevel)]"
                  class="d-flex align-center"
                >
                  <vsoc-icon
                    type="fill"
                    icon="icon-gaojingjibiebiaozhi"
                    size="1.2rem"
                    :style="{
                      color: alertLevel[Number(item.alarmLevel)].color,
                    }"
                  ></vsoc-icon>
                </div>
                <div
                  v-show-tips="item.name"
                  class="my-2"
                  style="max-width: 14rem; padding-left: 2px; font-weight: 600"
                >
                  {{ item.name }}
                </div>
              </div>
              <div
                v-else-if="item.name && itemKey === 'C018'"
                class="d-flex align-center"
              >
                <div
                  v-if="item.type && item.type > 0"
                  class="d-flex align-center"
                >
                  <v-img
                    :src="getCountryImage(item.type)"
                    contain
                    class="my-2 mr-2"
                  ></v-img>
                </div>
                <div
                  v-show-tips="item.name"
                  style="max-width: 14rem; font-weight: 600"
                >
                  {{ item.name }}
                </div>
              </div>
              <div v-else>N/A</div>
            </template>

            <template v-slot:item.alarmLevel="{ item }">
              <div
                v-if="item.alarmLevel && alertLevel[Number(item.alarmLevel)]"
                class="d-flex align-center"
              >
                <vsoc-icon
                  type="fill"
                  icon="icon-gaojingjibiebiaozhi"
                  size="1.2rem"
                  :style="{ color: alertLevel[Number(item.alarmLevel)].color }"
                  class="mr-2"
                ></vsoc-icon>
                <span>{{ alertLevel[Number(item.alarmLevel)].text }}</span>
              </div>
              <div v-else>N/A</div>
            </template>

            <template v-slot:item.alarmDate="{ item }">
              <div>{{ item.alarmDate | toDate }}</div>
            </template>

            <!-- Henry -->

            <template v-slot:item.value="{ item }">
              <div>
                {{ numberToFormat(item.value) }} ({{ item.percentage }})
              </div>
            </template>

            <template v-slot:item.status="{ item }">
              <div v-if="alertStatus[item.status]">
                <v-badge
                  dot
                  inline
                  offset-x="10"
                  :offset-y="-18"
                  :color="alertStatus[item.status].color"
                  class="mr-1"
                ></v-badge>
                <span>{{ alertStatus[item.status].text }}</span>
              </div>
              <span v-else>N/A</span>
            </template>

            <template v-slot:item.topNum="{ item, index }">
              <div
                :style="{ background: getTopColor(index + 1, itemKey) }"
                class="risk-num-sort"
              >
                {{ index + 1 }}
              </div>
            </template>

            <template v-slot:item.riskLevel="{ item }">
              <div :style="{ color: riskColor[item.riskLevel] }">
                {{ item.riskLevelName }}
              </div>
            </template>
            <template v-slot:item.riskStatus="{ item }">
              <div
                v-if="item.statusName"
                :style="{ color: riskColor[item.riskStatus] }"
              >
                {{ item.statusName }}
              </div>
              <span v-else>/</span>
            </template>
            <template v-slot:item.riskRemark="{ item }">
              <v-btn class="risk-small-btn" small icon @click.stop="">
                <vsoc-icon
                  type="fill"
                  size="large"
                  class="color--primary"
                  icon="icon-shenheren1"
                ></vsoc-icon>
              </v-btn>
            </template>
          </v-data-table>
        </vue-seamless-scroll>
      </template>
      <template v-else>
        <div
          class="fs-16 color--primary h-100 d-flex justify-center align-center"
        >
          None
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { numberToFormat } from '@/util/filters'
import defaultImage from '@/assets/images/svg/default.svg'
//    defaultCountry: require('@/assets/images/svg/default.svg'),

import countTo from 'vue-count-to'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { riskColor, riskNumColor, getTopColor } from '../util/defaultPreview'
import { getRoundSize } from './chart'
export default {
  name: 'CardItem4',
  props: {
    title: {
      type: String,
      default: '',
    },
    itemKey: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    headers1: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  components: {
    countTo,
    vueSeamlessScroll,
  },
  computed: {
    list1() {
      return this.list
    },
    // 获取告警等级颜色
    alertLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    // 获取告警等级颜色
    alertLevelList() {
      return Object.assign([], this.$store.state.enums.enums.AlarmLevel)
    },
    alertStatus() {
      return this.$store.getters['enums/getAlertStatus']
    },
    alertStatusEum() {
      return this.$store.state.enums.enums.AlarmStatus
    },
    headers() {
      return [
        {
          text: this.$t('alert.headers.triggered'),
          value: 'alarmDate',
          width: '30%',
        },
        {
          text: this.$t('alert.headers.type'),
          value: 'name',
          width: '30%',
        },
        // {
        //   text: '告警级别',
        //   value: 'alarmLevel',
        //   width: '10%',
        // },
        {
          text: this.$t('alert.headers.status'),
          value: 'status',
          width: '10%',
        },
        {
          text: this.$t('alert.headers.asset'),
          value: 'vehicleId',
          width: '20%',
        },
      ]
    },
  },
  mounted() {
    this.getHeight()
    window.addEventListener('resize', this.getHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getHeight)
  },
  data() {
    return {
      numberToFormat,
      riskNumColor,
      getTopColor,
      riskColor,
      classOption: {
        step: 0.3, // 数值越大速度滚动越快
        limitMoveNum: 10000, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
      },
    }
  },
  methods: {
    getRoundSize,
    getHeight() {
      setTimeout(() => {
        let boxContentHeight =
          this.$refs['ref' + this.itemKey]?.offsetHeight -
          this.$refs.boxHeader.offsetHeight -
          (document.getElementsByClassName('table-card-1')?.[0]?.offsetHeight +
            8)

        let tableHeight =
          document.getElementsByClassName('table-card-2')?.[0]?.offsetHeight

        this.$refs.seamlessScroll && this.$refs.seamlessScroll.reset()

        if (tableHeight > boxContentHeight) {
          this.classOption.limitMoveNum = this.list1.length
        } else {
          this.classOption.limitMoveNum = 10000
        }
      }, 300)
    },
    getCountryImage(countryCode) {
      try {
        return require(`@/assets/images/svg/${countryCode}.svg`)
      } catch (e) {
        return defaultImage // 使用导入的默认图片
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.row,
.col {
  padding: 0;
  margin: 0;
}
.risk-small-btn.v-btn--icon.v-size--small {
  width: 30px !important;
  height: 30px !important;
}
</style>
