<template>
  <div>
    <v-card>
      <div>
        <v-tabs v-model="userTab" left class="elevation-0">
          <v-tab
            v-for="tab in showAssetList ? tabs1 : tabs2"
            :key="tab.icon"
            class="px-10"
          >
            <!-- <v-icon size="20" class="me-3">
              {{ tab.icon }}
            </v-icon> -->
            <span class="font-weight-semibold">{{ tab.title }}</span>
          </v-tab>
        </v-tabs>
      </div>

      <v-tabs-items id="user-tabs-content" v-model="userTab">
        <v-tab-item>
          <!--     告警资产     -->
          <v-data-table
            v-if="!showAssetList"
            v-model="selected"
            :headers="header2"
            :items="list"
            height="36rem"
            item-key="id"
            class="mt-3"
            hide-default-footer
            :items-per-page="list.length"
            @click:row="onClickAlertRow"
          >
            <template v-slot:item.alarmLevel="{ item }">
              <div
                class="d-flex align-center"
                v-if="alertLevel[item.alarmLevel]"
              >
                <vsoc-icon
                  size="large"
                  :style="{ color: `${alertLevel[item.alarmLevel].color}` }"
                  type="fill"
                  class="mr-2"
                  icon="icon-gaojingjibiebiaozhi"
                >
                </vsoc-icon>
                <span>{{ alertLevel[item.alarmLevel].text }}</span>
              </div>
            </template>
            <template v-slot:item.vehicleId="{ item }">
              <div v-if="item.vehicleId">
                <div
                  v-if="item.assetType"
                  class="py-2 text-overflow-hide d-flex align-center"
                >
                  <vsoc-icon
                    v-if="assetType[item.assetType]"
                    :icon="assetType[item.assetType].icon"
                    type="fill"
                    class="primary--text"
                  >
                  </vsoc-icon>
                  <span v-show-tips class="pl-2">{{ item.vehicleId }}</span>
                </div>
              </div>
              <div v-else>多项资产</div>
            </template>

            <template v-slot:item.status="{ item }">
              <div v-if="alertStatus[item.status]" class="ml-n2">
                <v-icon :color="alertStatus[item.status].color" size="2.5rem">
                  mdi-circle-medium
                </v-icon>
                <span class="ml-n1">
                  {{ alertStatus[item.status].text }}
                </span>
              </div>
              <span v-else>N/A</span>
            </template>
            <template v-slot:item.alarmDate="{ item }">
              <span v-show-tips>
                {{ item.alarmDate | toDate }}
              </span>
            </template>
            <template v-slot:item.longitude="{ item }">
              <v-btn icon @click.native.stop="$_showLocation(item)">
                <v-icon size="1.25rem" color="grey darken-2"> mdi-map </v-icon>
              </v-btn>
            </template>
          </v-data-table>
          <vsoc-pagination
            v-if="!showAssetList"
            :page.sync="advanceQuery.pageNum"
            :size.sync="advanceQuery.pageSize"
            :total="total"
            @change-page="refresh"
            @change-size="refresh"
          >
          </vsoc-pagination>
          <!-- 影响资产 -->
          <v-data-table
            v-if="showAssetList"
            v-model="selected"
            :headers="header1"
            :items="list"
            height="36rem"
            item-key="id"
            :items-per-page="list.length"
            hide-default-footer
            class="mt-3"
            :loading="tableLoading"
            @click:row="onClickAssetRow"
          >
            <template #[`item.assetType`]="{ item }">
              <!-- <v-avatar
              size="30"
              :class="`v-avatar-light-bg primary--text`"
            > -->
              <div class="d-flex" v-if="assetType[item.assetType]">
                <vsoc-icon
                  :icon="assetType[item.assetType].icon"
                  class="mr-2 iconfont primary--text"
                  type="fill"
                >
                </vsoc-icon>
                <span>{{ assetType[item.assetType].text }}</span>
              </div>
            </template>
            <template v-slot:item.vehicleId="{ item }">
              <div class="py-2 text-overflow-hide d-flex align-center">
                <div v-show-tips style="max-width: 160px">
                  {{ item.vehicleId }}
                </div>
                <div @click.stop>
                  <v-btn
                    v-copy="item.vehicleId"
                    v-show-tips="$t('action.copy')"
                    icon
                  >
                    <vsoc-icon
                      type="fill"
                      class="secondary--text"
                      icon="icon-fuzhi"
                    ></vsoc-icon>
                  </v-btn>
                </div>
              </div>
            </template>

            <template v-slot:item.status="{ item }">
              <v-chip
                v-show-tips
                small
                class="font-weight-semibold px-2"
                :color="alertStatus[item.status].background"
                :text-color="alertStatus[item.status].color"
              >
                {{ alertStatus[item.status].text }}
              </v-chip>
            </template>
            <template v-slot:item.alarmDate="{ item }">
              <span v-show-tips>
                {{ item.createDate | toDate }}
              </span>
            </template>
            <template v-slot:item.longitude="{ item }">
              <v-btn icon @click.stop="$_showLocation(item)">
                <v-icon size="1.25rem" color="grey darken-2"> mdi-map </v-icon>
              </v-btn>
            </template>
            <!-- Balance -->
            <template #[`item.status`]="{ item }">
              <span v-if="typeof item.status === 'number'">
                ${{ item.status }}</span
              >
              <v-chip
                v-else
                small
                :color="chipColor[item.status]"
                :class="`v-chip-light-bg ${
                  chipColor[item.status]
                }--text font-weight-semibold text-sm`"
              >
                {{ alertStatus[item.status].text }}
              </v-chip>
            </template>
          </v-data-table>
          <vsoc-pagination
            v-if="showAssetList"
            :page.sync="advanceQuery.pageNum"
            :size.sync="advanceQuery.pageSize"
            :total="total"
            @change-page="refresh"
            @change-size="refresh"
          >
          </vsoc-pagination>
        </v-tab-item>
      </v-tabs-items>
    </v-card>
    <!-- 地图组件 -->
    <!-- <vsoc-map-sheet
      :value="showMap"
      :map-info="mapInfo"
      :zoom="[6, 15]"
      :is-drawer="true"
      @input="showMap = false"
    ></vsoc-map-sheet> -->
  </div>
</template>

<script>
import { getCurrentInstance, ref } from '@vue/composition-api'
// eslint-disable-next-line object-curly-newline
// import VsocMapSheet from '@/components/VsocMapSheet.vue'
import VsocPagination from '@/components/VsocPagination.vue'
import router from '@/router'
import store from '@/store'
import { mdiTrendingUp } from '@mdi/js'
export default {
  components: {
    VsocPagination,
    // VsocMapSheet,
  },
  computed: {
    alertStatus() {
      return store.state.enums.enums.AlarmStatus
    },

    // 获取告警等级颜色
    alertLevel() {
      return store.state.enums.enums.AlarmLevel
    },

    assetType() {
      return store.state.enums.enums.AssetType
    },
    tabs1() {
      return [
        {
          icon: 'mdi-car-off',
          title: this.$t('analytics.importAssets'),
        },
      ]
    },
    header1() {
      return [
        {
          text: this.$t('global.assetType'),
          value: 'assetType',
          width: '25%',
        },
        {
          text: this.$t('asset.headers.assetId'),
          value: 'vehicleId',
          width: '25%',
        },
        // { text: 'VIN', value: 'vehicleVin' },
        {
          text: '车企',
          value: 'vehicleCompanyName',
          width: '25%',
        },
        // {
        //   text: this.$t('asset.headers.year'),
        //   value: 'vehicleYear',
        // },
        {
          text: this.$t('asset.headers.count'),
          value: 'count',
        },
      ]
    },
    tabs2() {
      return [
        {
          icon: 'mdi-flash-alert',
          title: this.$t('analytics.alertRecord'),
        },
      ]
    },
    header2() {
      return [
        { text: this.$t('alert.headers.severity'), value: 'alarmLevel' }, // 告警级别
        {
          text: 'ID',
          align: 'start',
          value: 'id',
        },

        // { text: '告警大类', value: 'alertType' },

        {
          text: this.$t('alert.headers.type'),
          value: 'name',
          width: '30%',
        },
        {
          text: this.$t('alert.headers.asset'),
          value: 'vehicleId',
          width: '20%',
        },
        // { text: '最后位置', value: 'longitude', width: '120px' },
        {
          text: this.$t('alert.headers.status'),
          value: 'status',
          width: '120px',
        },
        {
          text: this.$t('alert.headers.triggered'),
          value: 'alarmDate',
        },
      ]
    },
  },
  props: {
    showAssetList: {
      type: Boolean,
      default: true,
    },
    // affectAssetList: {
    //   type: Array,
    //   default: [],
    // },
    // alarmRecordList: {
    //   type: Array,
    //   default: [],
    // },
    advanceQuery: {
      type: Object,
      default: {},
    },
    // alarmRecordTotal: {
    //   type: Number,
    //   default: 0,
    // },
    // affectAssetTotal: {
    //   type: Number,
    //   default: 0,
    // },
    list: {
      type: Array,
      default: [],
    },
    total: {
      type: Number,
      default: 0,
    },
  },
  setup(props, { emit }) {
    const vm = getCurrentInstance().proxy
    const userTab = vm.$t('analytics.importAssets')
    // const tabs = computed(() => {
    //   return [
    //     {
    //       icon: 'mdi-flash-alert',
    //       title: vm.$t('analytics.alertRecord'),
    //       isShow: !props.showAssetList,
    //     },
    //     {
    //       icon: 'mdi-car-off',
    //       title: vm.$t('analytics.importAssets'),
    //       isShow: !props.showAssetList,
    //     },
    //   ]
    // })
    let tableColumnHeaders
    let tabs
    if (props.showAssetList) {
      tabs = [
        {
          icon: 'mdi-car-off',
          title: vm.$t('analytics.importAssets'),
        },
      ]
      tableColumnHeaders = [
        {
          text: vm.$t('global.assetType'),
          value: 'assetType',
          width: '15%',
        },
        {
          text: vm.$t('asset.headers.assetId'),
          value: 'vehicleId',
          width: '20%',
        },
        { text: 'VIN', value: 'vehicleVin' },
        {
          text: vm.$t('asset.headers.model'),
          value: 'vehicleCompanyCode',
          width: '20%',
        },
        {
          text: vm.$t('asset.headers.year'),
          value: 'vehicleYear',
        },
        {
          text: vm.$t('asset.headers.count'),
          value: 'count',
        },
      ]
    } else {
      tabs = [
        {
          icon: 'mdi-flash-alert',
          title: vm.$t('analytics.alertRecord'),
        },
      ]
      tableColumnHeaders = [
        { text: vm.$t('alert.headers.severity'), value: 'alarmLevel' }, // 告警级别
        {
          text: 'ID',
          align: 'start',
          value: 'id',
        },

        // { text: '告警大类', value: 'alertType' },

        {
          text: vm.$t('alert.headers.type'),
          value: 'name',
          width: '30%',
        },
        {
          text: vm.$t('alert.headers.asset'),
          value: 'vehicleId',
          width: '20%',
        },
        // { text: '最后位置', value: 'longitude', width: '120px' },
        {
          text: vm.$t('alert.headers.status'),
          value: 'status',
          width: '120px',
        },
        {
          text: vm.$t('alert.headers.triggered'),
          value: 'alarmDate',
        },
      ]
    }

    const selected = ref([])
    const chipColor = {
      0: 'info',
      handling: 'danger',
    }

    // const tableColumnHeaders1 = computed(() => {
    //   return [
    //     {
    //       text: '资产类型',
    //       value: 'assetType',
    //       width: '15%',
    //     },
    //     {
    //       text: '资产编号',
    //       value: 'vehicleId',
    //       width: '20%',
    //     },
    //     { text: 'VIN', value: 'vehicleVin' },
    //     {
    //       text: '车型',
    //       value: 'vehicleModel',
    //       width: '20%',
    //     },
    //     {
    //       text: '年份',
    //       value: 'vehicleYear',
    //     },
    //     {
    //       text: '告警数量',
    //       value: 'count',
    //     },
    //   ]
    // })

    const userlist1 = []

    // const tableColumnHeaders = [
    //   { text: '级别', value: 'alarmLevel' }, // 告警级别
    //   {
    //     text: 'ID',
    //     align: 'start',
    //     value: 'id',
    //   },
    //
    //   // { text: '告警大类', value: 'alertType' },
    //
    //   {
    //     text: '告警类型',
    //     value: 'name',
    //     width: '30%',
    //   },
    //   {
    //     text: '影响资产',
    //     value: 'vehicleId',
    //     width: '20%',
    //   },
    //   // { text: '最后位置', value: 'longitude', width: '120px' },
    //   {
    //     text: '状态',
    //     value: 'status',
    //     width: '120px',
    //   },
    //   {
    //     text: '触发时间',
    //     value: 'alarmDate',
    //   },
    // ]

    const userlist = []

    const onClickAlertRow = item => {
      router.push({
        name: '告警详情',
        query: { id: item.id, vehicleId: item.vehicleId },
      })
    }

    const onClickAssetRow = item => {
      // 如果从威胁分析跳转则直接获取告警id
      sessionStorage.setItem('isRefreshed', 'Analytics')
      router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          vehicleId: item.vehicleId,
          vehicleCompanyCode: item.vehicleCompanyCode,
          startDate: props.advanceQuery.startDate,
          endDate: props.advanceQuery.endDate,
        },
      })
    }
    const tableLoading = ref(false)
    const refresh = () => {
      tableLoading.value = true
      emit(
        'refresh',
        {
          pageNum: props.advanceQuery.pageNum,
          pageSize: props.advanceQuery.pageSize,
        },
        () => {
          tableLoading.value = false
        },
      )
    }

    let showMap = ref(false)

    let mapInfo = ref({})

    const $_showLocation = item => {
      if (!item.longitude && !item.latitude) {
        vm.$notify.info('error', '地址为空！')
        return
      }
      mapInfo.value = {
        assetsId: item.vehicleId,
        center: {
          lng: item.longitude,
          lat: item.latitude,
        },
        icon: 'mdi-car',
        showLabel1: false,
        showLabel2: false,
      }
      showMap.value = true
    }

    return {
      userTab,
      tabs,
      selected,
      tableColumnHeaders,
      userlist,
      // tableColumnHeaders1,
      userlist1,
      chipColor,
      onClickAlertRow,
      onClickAssetRow,
      icons: {
        mdiTrendingUp,
      },
      refresh,
      showMap,
      mapInfo,
      $_showLocation,
      tableLoading,
    }
  },
}
</script>
