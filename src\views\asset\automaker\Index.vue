<template>
  <div>
    <bread-crumb></bread-crumb>
    <v-card tile class="main-content">
      <v-card-text class="pa-0">
        <div class="d-flex justify-space-between align-center mb-3">
          <div class="d-flex align-center">
            <v-btn
              v-has:automaker-new
              color="primary"
              elevation="0"
              class="me-3"
              @click="add"
            >
              新增
            </v-btn>
            <div class="d-flex align-center ml-2">
              <v-icon small class="mr-1" color="grey"
                >mdi-format-list-bulleted</v-icon
              >
              <span class="text-body-2 grey--text">
                共 {{ tableDataTotal }} 条
              </span>
            </div>
          </div>
          <div class="d-flex align-end">
            <table-search
              :searchList="searchList"
              :searchQuery="query"
              @search="$_search"
            ></table-search>
          </div>
        </div>
        <v-data-table
          fixed-header
          :items-per-page="-1"
          item-key="id"
          :height="tableHeight"
          hide-default-footer
          :headers="headers"
          :items="tableData"
          class="table border-radius-xl mt-3 thead-light pb-5"
          :loading="tableLoading"
          :sort-by="['risk_score']"
          :sort-desc="[true]"
        >
          <!-- ID省略显示 -->
          <!-- <template v-slot:item.id="{ item }">
            <span class="text-caption" :title="item.id">
              {{
                item.id.length > 12 ? item.id.substring(0, 12) + '...' : item.id
              }}
            </span>
          </template> -->

          <!-- 品牌列合并LOGO和品牌名称 -->
          <template v-slot:item.brand="{ item }">
            <div class="d-flex align-center py-2">
              <v-avatar
                v-if="item.picture_code"
                size="32"
                class="logo-avatar mr-3"
              >
                <img :src="item.picture_code" alt="LOGO" class="logo-image" />
              </v-avatar>
              <v-avatar v-else size="32" class="logo-placeholder mr-3">
                <v-icon color="grey lighten-1" size="18">mdi-domain</v-icon>
              </v-avatar>
              <span class="brand-name font-weight-medium">{{
                item.brand
              }}</span>
            </div>
          </template>
          <template v-slot:item.automaker_name="{ item }">
            <div v-show-tips style="max-width: 16rem">
              {{ item.automaker_name }}
            </div>
          </template>
          <template v-slot:item.automaker_code="{ item }">
            <div v-show-tips style="max-width: 8rem">
              {{ item.automaker_code }}
            </div>
          </template>
          <!-- 态势 -->
          <template v-slot:item.healthStatus="{ item }">
            <div
              class="d-flex align-center"
              v-if="vehicleStatus[item.healthStatus]"
              :style="{ color: `${vehicleStatus[item.healthStatus].color}` }"
            >
              <vsoc-icon type="fill" icon="icon-dunpai"></vsoc-icon>
              <span class="text-no-wrap ml-2">{{
                vehicleStatus[item.healthStatus].text1
              }}</span>
            </div>
          </template>

          <!-- 风险评分显示 -->
          <template v-slot:item.risk_score="{ item }">
            <div class="text-center">
              <span class="risk-score-text">{{ item.risk_score || '-' }}</span>
            </div>
          </template>

          <!-- 风险评级显示 -->
          <template v-slot:item.risk_level="{ item }">
            <div class="risk-level-container">
              <v-icon
                :color="
                  getRiskLevelInfo(
                    item.risk_score,
                    item.is_supervise,
                    item.risk_details,
                  ).color
                "
                size="20"
                class="risk-level-shield"
              >
                {{
                  item.is_supervise === '0' ? 'mdi-shield' : 'mdi-shield-off'
                }}
              </v-icon>
              <span
                class="risk-level-text"
                :style="{
                  color: getRiskLevelInfo(
                    item.risk_score,
                    item.is_supervise,
                    item.risk_details,
                  ).color,
                }"
              >
                {{
                  getRiskLevelInfo(
                    item.risk_score,
                    item.is_supervise,
                    item.risk_details,
                  ).text
                }}
              </span>
            </div>
          </template>

          <!-- 风险统计显示 -->
          <template v-slot:item.risk_details="{ item }">
            <div
              class="d-flex align-center"
              v-if="item.risk_details"
              @click="goAlert(item)"
            >
              <div v-if="item.totalNum > 0" class="cursor-pointer">
                <v-tooltip
                  v-for="(alarmItem, index) in item.riskList"
                  :key="index"
                  bottom
                  v-if="alarmItem.value > 0"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-avatar
                      class="mr-2 cursor-pointer risk-avatar"
                      :color="alarmItem.color"
                      size="2rem"
                      v-bind="attrs"
                      v-on="on"
                    >
                      <span class="text-body-2 font-weight-bold white--text">{{
                        alarmItem.value
                      }}</span>
                    </v-avatar>
                  </template>
                  <span
                    >{{ getRiskLevelName(alarmItem.color) }}:
                    {{ alarmItem.value }} 个</span
                  >
                </v-tooltip>
              </div>
              <div
                v-else-if="item.is_supervise === '1'"
                class="text-caption grey--text"
              >
                -
              </div>
              <div v-else class="text-caption grey--text">暂无风险</div>
            </div>
          </template>

          <template v-slot:item.is_supervise="{ item }">
            <v-chip
              :color="item.is_supervise === '0' ? 'success' : 'grey'"
              :outlined="item.is_supervise !== '0'"
              small
              :text-color="item.is_supervise === '0' ? 'white' : 'grey'"
              class="supervise-chip"
              :class="{ 'supervise-weak': item.is_supervise !== '0' }"
            >
              {{ item.is_supervise === '0' ? '是' : '否' }}
            </v-chip>
          </template>

          <!-- 更新时间格式化显示 -->
          <template v-slot:item.update_date="{ item }">
            <span class="text-caption">
              {{ formatDate(item.update_date) }}
            </span>
          </template>
          <template v-slot:item.headquarters_location="{ item }">
            <div v-show-tips style="max-width: 8rem">
              {{ item.headquarters_location }}
            </div>
          </template>
          <template v-slot:item.address="{ item }">
            <div v-show-tips style="max-width: 12rem">
              {{ item.address }}
            </div>
          </template>
          <template v-slot:item.is_active="{ item }">
            <v-chip
              :color="item.is_active === 1 ? 'success' : 'error'"
              small
              text-color="white"
            >
              {{ item.is_active === 1 ? '启用' : '禁用' }}
            </v-chip>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-btn
              v-has:automaker-edit
              icon
              @click="onEdit(item)"
              v-show-tips="'编辑'"
            >
              <vsoc-icon
                type="fill"
                icon="icon-bianji"
                class="action-btn"
                size="x-large"
              >
              </vsoc-icon>
            </v-btn>
            <v-btn
              v-has:automaker-del
              icon
              v-show-tips="'删除'"
              @click="onDel(item)"
            >
              <vsoc-icon
                type="fill"
                class="action-btn"
                icon="icon-shanchu"
                size="x-large"
              ></vsoc-icon>
            </v-btn>
          </template>
        </v-data-table>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { deleteAutomaker, getAutomakerList } from '@/api/asset/automaker'
import TableSearch from '@/components/TableSearch/index.vue'
import breadCrumb from '@/components/bread-crumb/index'
import { setRemainingHeight } from '@/util/utils'
import { getAutomakerSecurityLevelUnified } from '@/util/algorithm'

export default {
  name: 'AutomakerIndex',
  components: {
    breadCrumb,
    TableSearch,
  },
  data() {
    return {
      // 查询条件
      query: {
        automaker_name: '',
        is_supervise: '0',
      },

      tableData: [],
      tableHeight: '34.5rem',
      tableLoading: false,
      btnLoading: false,
      tableDataTotal: 0,
    }
  },
  computed: {
    searchList() {
      return [
        {
          type: 'input',
          value: 'automaker_name',
          text: '车企名称',
        },
        {
          type: 'select',
          value: 'is_supervise',
          text: '已监管',
          itemList: [
            { text: '全部', value: '' },
            { text: '是', value: '0' },
            { text: '否', value: '1' },
          ],
        },
      ]
    },
    headers() {
      return [
        // { text: 'ID', value: 'id', width: '100px', sortable: false },
        { text: '品牌', value: 'brand', width: '150px', sortable: false },
        {
          text: '车企名称',
          value: 'automaker_name',
          width: '200px',
          sortable: false,
        },
        // { text: '车企编码', value: 'automaker_code', width: '120px' },

        { text: '所在地', value: 'headquarters_location', width: '100px' },

        {
          text: '风险评级',
          value: 'risk_level',
          width: '120px',
          sortable: false,
        },
        {
          text: '风险评分',
          value: 'risk_score',
          width: '100px',
          sortable: true,
        },
        // { text: '状态', value: 'is_active', width: '80px', sortable: false },
        {
          text: '风险统计',
          value: 'risk_details',
          width: '230px',
          sortable: false,
        },

        {
          text: '已监管',
          value: 'is_supervise',
          width: '80px',
          sortable: true,
        },
        {
          text: '更新时间',
          value: 'update_date',
          width: '150px',
          sortable: true,
        },

        {
          text: '操作',
          value: 'actions',
          sortable: false,
          width: '110px',
        },
      ]
    },
  },
  created() {
    this.$_setTableHeight()
    this.$bus.$on('resize', this.$_setTableHeight)
    this.$_getTableData()
  },
  beforeDestroy() {
    this.$bus.$off('resize', this.$_setTableHeight)
  },
  methods: {
    goAlert(record) {
      // 跳转到告警详情页面或相关页面
      this.$router.push({
        path: '/alerts',
        query: {
          isQuery: 1,
          vehicleCompanyCode: record.automaker_code,
          statusList: JSON.stringify(['0', '1']),
          alarmLevelList: JSON.stringify(['0', '1', '2', '3', '4']),
        },
      })
    },

    // 获取风险评级信息（统一版）
    getRiskLevelInfo(riskScore, isSupervise, riskDetails = null) {
      // 如果未监管，返回默认的未监管状态
      if (isSupervise !== '0') {
        return {
          color: '#B4BBCC',
          text: '-',
          level: 'Unsupervised',
        }
      }

      // 使用统一的智能算法
      return getAutomakerSecurityLevelUnified({
        riskDetails: riskDetails,
        riskScore: riskScore,
        scoreType: 'auto',
      })
    },

    // 格式化日期显示
    formatDate(dateString) {
      if (!dateString) return '-'
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })
      } catch (error) {
        return dateString
      }
    },
    // 获取风险级别名称
    getRiskLevelName(color) {
      const riskLevels = {
        '#DA1F1F': '严重',
        '#FA9114': '高',
        '#F5D018': '中',
        '#12B2A7': '低',
        '#B4BBCC': '未分配',
      }
      return riskLevels[color] || '未知'
    },
    onClear() {
      this.query.automaker_name = ''
      this.query.is_supervise = ''
      this.$_search()
    },
    onDel(record) {
      this.$swal({
        title: '确认删除',
        text: `确定要删除车企"${record.automaker_name}"吗？`,
        icon: 'warning',
        reverseButtons: true,
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        customClass: {
          confirmButton: 'sweet-btn-primary',
        },
      }).then(async result => {
        if (result.isConfirmed) {
          try {
            const params = {
              id: record.id,
            }
            await deleteAutomaker(params)
            this.$notify.info('success', '删除车企成功')
            this.$_getTableData()
          } catch (error) {
            this.$notify.info('error', '删除车企失败')
          }
        }
      })
    },
    add() {
      this.$router.push('/automaker/add')
    },
    onEdit(record) {
      this.$router.push(`/automaker/edit?id=${record.id}`)
    },
    async $_getTableData() {
      try {
        this.tableLoading = true
        this.tableData = []

        // 调用真实的后台接口（无分页版本）
        const res = await getAutomakerList(this.query)

        // 数据字段映射：后台字段 -> 前端字段
        // 无分页接口返回的是数组，不是 {records: []} 格式
        const dataList = Array.isArray(res.data) ? res.data : res.data || []
        this.tableData = dataList.map(item => ({
          id: item.id,
          brand: item.brand,
          automaker_name: item.automakerName,
          automaker_code: item.automakerCode,
          picture_code: item.pictureCode,
          description: item.description,
          is_supervise: item.isSupervise,
          headquarters_location: item.headquartersLocation,
          address: item.address,
          is_active: item.isActive,
          create_user: item.createUser,
          create_date: item.createDate,
          update_user: item.updateUser,
          update_date: item.updateDate,
          preview_attachments: item.previewAttachments || [],
          risk_score: item.riskScore,
          risk_details: item.riskDetails,
        }))

        // 处理风险明细数据，计算百分比和颜色配置
        this.tableData.forEach(v => {
          if (v.risk_details) {
            v.totalNum =
              Number(v.risk_details.critical) +
              Number(v.risk_details.high) +
              Number(v.risk_details.medium) +
              Number(v.risk_details.low) +
              Number(v.risk_details.unassigned)
            if (v.totalNum > 0) {
              v.riskList = [
                {
                  value: v.risk_details.critical,
                  color: '#DA1F1F',
                },
                {
                  value: v.risk_details.high,
                  color: '#FA9114',
                },
                {
                  value: v.risk_details.medium,
                  color: '#F5D018',
                },
                {
                  value: v.risk_details.low,
                  color: '#12B2A7',
                },
                {
                  value: v.risk_details.unassigned,
                  color: '#B4BBCC',
                },
              ]
            }
          }
        })

        this.tableDataTotal = this.tableData.length
        // console.log(this.tableData)
      } catch (e) {
        console.error(`获取车企管理数据：${e}`)
        this.$notify.info('error', '获取车企数据失败')
      }
      this.tableLoading = false
    },

    $_search() {
      this.$_getTableData()
    },

    $_setTableHeight() {
      this.$nextTick(() => {
        this.tableHeight = setRemainingHeight() + 17
      })
    },
  },
}
</script>

<style scoped>
/* LOGO显示样式优化 */
.logo-avatar {
  border: 2px solid #e0e0e0;
  background-color: #fafafa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.logo-avatar:hover {
  border-color: #1976d2;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 2px;
}

.logo-placeholder {
  border: 2px dashed #e0e0e0;
  background-color: #f5f5f5;
}

.brand-name {
  color: #333;
  font-size: 14px;
}

/* 风险统计圆圈样式优化 */
.risk-avatar {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.risk-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 已监管状态样式优化 */
.supervise-chip {
  font-size: 11px;
}

.supervise-weak {
  opacity: 0.6;
  font-weight: normal;
}

/* 风险条形图Hover效果 */
.risk-progress-bar {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 2px;
}

.risk-progress-bar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1;
}

/* 风险评分文字样式 */
.risk-score-text {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

/* 风险评级样式 */
.risk-level-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.risk-level-shield {
  transition: all 0.3s ease;
}

.risk-level-shield:hover {
  transform: scale(1.1);
  filter: brightness(1.1);
}

.risk-level-text {
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.risk-level-text:hover {
  font-weight: 600;
}

/* 未监管状态样式 */
.risk-level-unsupervised {
  color: #b4bbcc !important;
  font-style: italic;
}

.risk-level-unsupervised .risk-level-shield {
  opacity: 0.6;
}

/* 表格行高优化 */
.v-data-table >>> tbody tr {
  height: 60px !important;
}

.v-data-table >>> tbody td {
  padding: 8px 16px !important;
  vertical-align: middle;
}

/* 品牌列内边距优化 */
.v-data-table >>> tbody td:first-child {
  padding: 0px 16px !important;
}
</style>
