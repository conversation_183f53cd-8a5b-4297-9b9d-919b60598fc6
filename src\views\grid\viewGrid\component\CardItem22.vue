<template>
  <!-- 定制化图形2 -->
  <div class="box">
    <div class="box-header">{{ title }}</div>
    <template v-if="list.length !== 0">
      <div
        class="h-100 w-100 d-flex flex-column justify-space-between"
        style="margin-top: 2%"
      >
        <div class="card-top-22 d-flex justify-space-between">
          <div
            v-for="(item, index) in list1"
            class="d-flex flex-column align-center"
            :key="index"
          >
            <div
              class="d-flex card-item-title-text align-center"
              style="margin-top: 2%"
            >
              {{ numberToFormat(item.value) }}
            </div>
            <div class="font-weight-medium fs-14">
              {{ item.name }}
            </div>
          </div>
        </div>

        <div v-if="list2.length !== 0" class="w-100 flex-1">
          <vsoc-chart
            :echartId="echartId"
            :option="chartOption"
            @highlight="onHighlight"
          ></vsoc-chart>
        </div>
      </div>
    </template>
    <template v-else>
      <div
        class="box-chart d-flex align-center color--primary justify-center fs-16"
      >
        None
      </div>
    </template>
  </div>
</template>

<script>
import VsocChart from '@/components/VsocChart.vue'
import { numberToFormat } from '@/util/filters'

import { getRoundSize, tooltip } from './chart'
export default {
  name: 'CardItem22',
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    echartId: {
      type: String,
      default: '',
    },
  },
  components: {
    VsocChart,
  },
  computed: {
    alarmLevel() {
      return this.$store.state.enums.enums.AlarmLevel
    },
    chartOption() {
      let list = []
      this.list2.forEach(item => {
        list.push({
          name: item.name,
          value: item.value,
          percentage: item.percentage,
          type: item.type,
        })
      })
      let total = this.list2.reduce((accumulator, currentValue) => {
        return accumulator + currentValue.value
      }, 0)
      const common = {
        type: 'pie',
        clockwise: false,
        emphasis: {
          scale: true,
          scaleSize: 2,
        },
        center: ['50%', '52%'],
        bottom: -getRoundSize(21),
        percentPrecision: 2,
      }
      const _this = this
      const option = {
        tooltip: {
          ...tooltip(),
          trigger: 'item',
          formatter(val) {
            return (
              val.marker +
              '\t' +
              val.name +
              ':\t' +
              val.value +
              '(' +
              val.percent +
              '%)'
            )
          },
        },
        backgroundColor: 'transparent',

        legend: {
          show: false,
        },

        title: {
          text: numberToFormat(total),
          position: 'center',
          top: '51.5%',
          left: 'center',
          // bottom: 'center',
          textStyle: {
            // lineHeight: getRoundSize(60),
            fontSize: getRoundSize(18),
            fontWeight: 600,
            // padding: [getRoundSize(108), 0, getRoundSize(8), getRoundSize(8)],
            // verticalAlign: 'right',
            color: '#fff',
          },
        },

        series: [
          {
            // name: '中间的环',
            ...common,
            // percentPrecision: 0,
            radius: this.isFull ? ['50%', '61%'] : ['40%', '51%'],
            // emphasis: { scale: true },
            data: list,
            // labelLayout: {
            //   hideOverlap: false,
            // },
            itemStyle: {
              normal: {
                opacity: 1,
                color: function (params) {
                  return (
                    _this.alarmLevel[Number(params.data.type)]?.color ||
                    '#32FDB8'
                  )
                },
              },
            },
            label: {
              normal: {
                position: 'outside',
                alignTo: 'edge',
                edgeDistance: '10%',
                formatter: function (params) {
                  if (params.name !== '') {
                    return `{legend|●}\t{name|${
                      params.name
                    }}\n{value|${numberToFormat(params.value)}\t(${
                      params.percent
                    }%)}`
                  } else {
                    return ''
                  }
                },
                rich: {
                  value: {
                    fontSize: getRoundSize(12),
                    lineHeight: getRoundSize(22),
                    // fontFamily: 'PingFang HK',
                    color: '#FFFFFF',
                    opacity: 0.8,
                    padding: [getRoundSize(8), 0],
                  },
                  name: {
                    fontSize: getRoundSize(12),
                    lineHeight: getRoundSize(22),
                    // fontFamily: 'PingFang HK',
                    color: '#FFFFFF',
                    opacity: 0.8,
                  },
                  legend: {
                    padding: [0, getRoundSize(2), 0, 0],
                    fontSize: getRoundSize(14),
                    color: 'inherit',
                  },
                },
              },
            },
            labelLine: {
              length: 8,
              length2: 5,
              lineStyle: {
                width: 1,
              },
            },
          },
        ],
      }
      return option
    },
  },
  watch: {},
  data() {
    return {
      numberToFormat,
      list1: this.list.find(v => v.value === '0')?.list || [],
      list2: this.list.find(v => v.value === '1')?.list || [],
    }
  },
  methods: {
    onHighlight() {},
  },
}
</script>
