<template>
  <!-- 安全态势 -->
  <div class="box py-0 px-1 align-center security-posture-container">
    <!-- 增强版安全盾牌区域 -->
    <div class="enhanced-shield-container">
      <!-- 背景光效 -->
      <!-- <div class="shield-glow-bg" :class="postureObj.level"></div> -->

      <!-- 能量粒子脉冲效果容器 -->
      <div class="energy-pulse-container">
        <!-- 第一层脉冲波 -->
        <div
          v-for="i in 8"
          :key="`pulse-${i}`"
          class="energy-pulse-particle"
          :class="postureObj.level"
          :style="{
            '--delay': i * 0.2 + 's',
            '--angle': i * 45 + 'deg',
          }"
        ></div>
        <!-- 第二层脉冲波（稍慢一些，形成层次感） -->
        <div
          v-for="i in 6"
          :key="`pulse-slow-${i}`"
          class="energy-pulse-particle-slow"
          :class="postureObj.level"
          :style="{
            '--delay': i * 0.3 + 's',
            '--angle': i * 60 + 'deg',
          }"
        ></div>
      </div>

      <!-- 主盾牌区域 -->
      <div
        class="animate enhanced-shield d-flex flex-column justify-center align-center"
      >
        <!-- 优化后的盾牌光环效果 -->
        <!-- <div class="shield-ring-enhanced" :class="postureObj.level"></div>
        <div class="shield-ring-outer-enhanced" :class="postureObj.level"></div> -->
        <!-- 添加内部装饰环 -->
        <div class="shield-inner-decoration" :class="postureObj.level"></div>

        <!-- 安全等级显示 -->
        <!-- <div
          v-if="postureObj.level"
          class="fs-64 enhanced-posture-text"
          :class="postureObj.level"
          :style="{ color: postureObj.color }"
        >
          {{ postureObj.text }}
        </div> -->

        <!-- 安全等级 -->
        <div v-if="postureObj.level" class="fs-64 enhanced-posture-text">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <div class="security-shield-wrapper" v-bind="attrs" v-on="on">
                <v-icon
                  size="5rem"
                  :color="postureObj.color"
                  class="security-shield"
                >
                  mdi-shield
                </v-icon>
              </div>
            </template>
            <span>{{ postureObj.text }}</span>
            <!-- <span>2222</span> -->
          </v-tooltip>
          <!-- <div class="text-body-2 grey--text">安全态势</div> -->
        </div>

        <!-- 安全态势文字 -->
        <div class="enhanced-posture-label">
          {{ $t('posture.serviceSituation') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DvBorderBox14 from './dv-border-box14/Index.vue'
import { getAutomakerSecurityLevelUnified } from '@/util/algorithm'

export default {
  name: 'CardItem12',
  props: {
    currentModelType: {
      type: String,
      default: '',
    },
    vehicleType: {
      type: Array,
      default: () => {
        return []
      },
    },
    // refreshDate: [Date, String],
    // posture: [String, Number],
  },
  components: {
    DvBorderBox14,
  },
  data() {
    return {
      postureObj: {
        color: '',
        level: '',
      },
    }
  },
  watch: {
    currentModelType: {
      handler(val) {
        this.getPosture(val)
      },
      deep: true,
    },
  },
  computed: {},
  mounted() {
    this.getPosture(this.currentModelType)
  },
  methods: {
    getPosture(val) {
      if (!val) return
      let findItem = this.vehicleType.find(v => v.value === val)
      if (findItem) {
        this.postureObj = getAutomakerSecurityLevelUnified({
          riskDetails: findItem.riskDetails,
          riskScore: findItem.riskScore,
        })
        console.log(this.postureObj)
      } else {
        this.postureObj = {
          color: '',
          level: '',
          text: '',
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
/* 安全盾牌样式 */
.security-shield-wrapper {
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.security-shield {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;

  &:hover {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  }
}

// 安全态势容器 - 调整整体位置，上移15%优化显示位置
.security-posture-container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center; // 保持居中对齐
  align-items: center;
  // 从向下移动8%改为向上移动15%，优化视觉位置
  transform: translateY(-34%);
  box-sizing: border-box;
}

// 增强版盾牌容器 - 改进自适应逻辑，优化垂直居中
.enhanced-shield-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  // 移除max-height限制，让盾牌能够自适应大屏
  // 外层容器已处理位移，这里不需要额外调整
  width: 100%;
}

// 背景光效 - 统一使用Good等级的蓝色光效，与深蓝背景协调
// .shield-glow-bg {
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%);
//   // 优化比例，确保在各种屏幕下都与盾牌尺寸协调
//   width: min(35vw, 450px); // 增大基础尺寸以适应大屏
//   height: min(35vw, 450px);
//   border-radius: 50%;
//   opacity: 0.4; // 增强基础透明度
//   animation: glow-pulse-enhanced 2.5s ease-in-out infinite alternate,
//     glow-rotate 8s linear infinite; // 添加旋转动画

//   // 统一使用蓝色光效，无论什么评级都保持协调
//   &.Strong,
//   &.Good,
//   &.Medium,
//   &.Fair,
//   &.Weak,
//   &.Poor {
//     background: radial-gradient(
//       circle,
//       rgba(68, 226, 254, 0.4) 0%,
//       // 统一使用标准蓝色
//       rgba(68, 226, 254, 0.1) 50%,
//       transparent 100%
//     );
//     box-shadow: 0 0 60px rgba(68, 226, 254, 0.3);
//   }
// }

// 能量粒子脉冲效果 - 从盾牌中心向外扩散的能量场效果
.energy-pulse-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: min(35vw, 450px);
  height: min(35vw, 450px);
  pointer-events: none;
}

// 主要脉冲粒子 - 快速扩散
.energy-pulse-particle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  transform: translate(-50%, -50%) rotate(var(--angle));
  animation: energy-pulse-expand 2.5s ease-out infinite;
  animation-delay: var(--delay);

  // 统一使用蓝色能量粒子，与背景光效协调
  &.Strong,
  &.Good,
  &.Medium,
  &.Fair,
  &.Weak,
  &.Poor {
    background: rgba(68, 226, 254, 0.9);
    box-shadow: 0 0 8px rgba(68, 226, 254, 0.6);
  }
}

// 慢速脉冲粒子 - 形成层次感
.energy-pulse-particle-slow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  transform: translate(-50%, -50%) rotate(var(--angle));
  animation: energy-pulse-expand-slow 3.5s ease-out infinite;
  animation-delay: var(--delay);

  // 统一使用蓝色慢速粒子，与背景光效协调
  &.Strong,
  &.Good,
  &.Medium,
  &.Fair,
  &.Weak,
  &.Poor {
    background: rgba(68, 226, 254, 0.6);
    box-shadow: 0 0 6px rgba(68, 226, 254, 0.4);
  }
}

// 增强版盾牌 - 优化笔记本和大屏的显示平衡，增强动画质感
.enhanced-shield {
  position: relative;
  // 调整宽度比例，让盾牌看起来更修长，不那么宽
  width: min(22vw, 320px); // 进一步减少宽度：25vw → 22vw
  height: min(40vw, 560px); // 保持高度不变，形成更好的宽高比
  max-width: 85%; // 稍微减少最大宽度限制
  // background: url('../images/<EMAIL>') center no-repeat;
  // background-size: contain;
  z-index: 2;
  // 增强盾牌动画效果
  animation: shield-float 4s ease-in-out infinite alternate,
    shield-glow-pulse 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(68, 226, 254, 0.3));

  // 优化盾牌质感，移除奇怪的长方形阴影
  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 10%;
  //   left: 10%;
  //   right: 10%;
  //   bottom: 10%;
  //   // 使用椭圆形渐变替代长方形，更符合盾牌形状
  //   background: radial-gradient(
  //     ellipse 80% 60% at center 40%,
  //     rgba(255, 255, 255, 0.08) 0%,
  //     rgba(255, 255, 255, 0.04) 40%,
  //     transparent 70%
  //   );
  //   border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  //   pointer-events: none;
  // }
}

// 一体化盾牌光环效果 - 与盾牌形状协调，实现统一动画
// .shield-ring-enhanced {
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%);
//   // 调整为椭圆形，更贴合修长的盾牌形状
//   width: min(25vw, 350px); // 相应减少宽度，与盾牌保持协调
//   height: min(35vw, 480px); // 保持高度比例与盾牌一致
//   border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%; // 椭圆形，模拟盾牌轮廓
//   opacity: 0.15; // 大幅降低透明度，作为微妙的光晕
//   animation: shield-aura-pulse 8s ease-in-out infinite alternate; // 与盾牌同步的呼吸动画

//   // 创建盾牌形状的光晕效果，而非圆形边框
//   &::before {
//     content: '';
//     position: absolute;
//     top: 5%;
//     left: 5%;
//     right: 5%;
//     bottom: 5%;
//     border-radius: 50% 50% 50% 50% / 65% 65% 35% 35%; // 与盾牌形状一致
//     background: radial-gradient(
//       ellipse 80% 60% at center 35%,
//       currentColor 0%,
//       transparent 40%,
//       currentColor 70%,
//       transparent 100%
//     );
//     opacity: 0.2; // 微妙的光晕效果
//     animation: shield-aura-shimmer 6s ease-in-out infinite alternate;
//   }

//   // 统一使用蓝色盾牌光环，与背景光效协调
//   &.Strong,
//   &.Good,
//   &.Medium,
//   &.Fair,
//   &.Weak,
//   &.Poor {
//     color: rgba(68, 226, 254, 0.25);
//     filter: blur(0.5px); // 添加轻微模糊，增强光晕效果
//   }
// }

// .shield-ring-outer-enhanced {
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%);
//   // 优化自适应尺寸，确保与盾牌保持协调比例
//   width: min(28vw, 380px); // 增大基础尺寸以适应大屏
//   height: min(28vw, 380px);
//   border-radius: 50%;
//   opacity: 0.3; // 增强透明度
//   animation: ring-rotate-reverse-enhanced 10s linear infinite,
//     ring-scale-pulse 4s ease-in-out infinite alternate;

//   // 虚线边框效果
//   &::before {
//     content: '';
//     position: absolute;
//     top: -1px;
//     left: -1px;
//     right: -1px;
//     bottom: -1px;
//     border-radius: 50%;
//     border: 1px dashed currentColor;
//     opacity: 0.5;
//   }

//   // 统一使用蓝色外环，与背景光效协调
//   &.Strong,
//   &.Good,
//   &.Medium,
//   &.Fair,
//   &.Weak,
//   &.Poor {
//     color: rgba(68, 226, 254, 0.5);
//     box-shadow: 0 0 10px rgba(68, 226, 254, 0.2);
//   }
// }

// 内部装饰环 - 优化自适应尺寸，确保与盾牌保持协调比例
.shield-inner-decoration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  // 优化比例，确保在各种屏幕下都与盾牌尺寸协调
  width: min(20vw, 260px); // 增大基础尺寸以适应大屏
  height: min(20vw, 260px);
  border-radius: 50%;
  opacity: 0.15;
  animation: ring-rotate-slow 15s linear infinite;

  // 使用径向渐变创建更自然的装饰效果
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background: radial-gradient(
      circle,
      transparent 60%,
      currentColor 70%,
      transparent 80%
    );
  }

  // 统一使用蓝色内部装饰环，与背景光效协调
  &.Strong,
  &.Good,
  &.Medium,
  &.Fair,
  &.Weak,
  &.Poor {
    color: rgba(68, 226, 254, 0.4);
    filter: drop-shadow(0 0 5px rgba(68, 226, 254, 0.3));
  }
}

// 增强版安全等级文字 - 增强发光和动画效果
.enhanced-posture-text {
  position: relative;
  z-index: 99;
  font-weight: 700;
  text-shadow: 0 0 20px currentColor, 0 0 40px currentColor;
  // animation: text-glow-enhanced 2s ease-in-out infinite alternate,
  //   text-scale-pulse 4s ease-in-out infinite;
  transition: all 0.3s ease;

  // 添加文字背景光效
  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 50%;
  //   left: 50%;
  //   transform: translate(-50%, -50%);
  //   width: 120%;
  //   height: 120%;
  //   background: radial-gradient(circle, currentColor 0%, transparent 70%);
  //   opacity: 0.1;
  //   border-radius: 50%;
  //   animation: text-bg-pulse 3s ease-in-out infinite alternate;
  //   z-index: -1;
  // }

  // 文字发光效果根据评级变化（唯一保持评级颜色差异的元素）
  &.Strong {
    text-shadow: 0 0 25px #2ee56a, 0 0 50px #2ee56a, 0 0 75px #2ee56a,
      0 0 100px rgba(46, 229, 106, 0.5);
  }

  &.Good {
    text-shadow: 0 0 25px #44e2fe, 0 0 50px #44e2fe, 0 0 75px #44e2fe,
      0 0 100px rgba(68, 226, 254, 0.5);
  }

  &.Medium,
  &.Fair {
    text-shadow: 0 0 25px #ff910f, 0 0 50px #ff910f, 0 0 75px #ff910f,
      0 0 100px rgba(255, 145, 15, 0.5);
  }

  &.Weak,
  &.Poor {
    text-shadow: 0 0 25px #da1f1f, 0 0 50px #da1f1f, 0 0 75px #da1f1f,
      0 0 100px rgba(218, 31, 31, 0.5);
  }
}

// 安全态势标签
.enhanced-posture-label {
  font-size: 28px;
  line-height: 39.2px;
  font-weight: 500;
  color: #cdeeff;
  margin: 16px 0 8px;
  opacity: 0.8;
  text-shadow: 0 0 10px #cdeeff;
  z-index: 3;
  position: relative;
}

// 动画关键帧定义
@keyframes glow-pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.5;
  }
}

// 能量脉冲扩散动画 - 主要脉冲粒子
@keyframes energy-pulse-expand {
  0% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(0px)
      scale(1);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-20px)
      scale(1.2);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-120px)
      scale(1.5);
  }
  80% {
    opacity: 0.3;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-180px)
      scale(0.8);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-220px)
      scale(0.5);
  }
}

// 能量脉冲扩散动画 - 慢速脉冲粒子
@keyframes energy-pulse-expand-slow {
  0% {
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(0px)
      scale(0.8);
    opacity: 0;
  }
  15% {
    opacity: 0.8;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-15px)
      scale(1);
  }
  60% {
    opacity: 0.6;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-140px)
      scale(1.2);
  }
  85% {
    opacity: 0.2;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-200px)
      scale(0.6);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(var(--angle)) translateY(-250px)
      scale(0.3);
  }
}

@keyframes ring-rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes ring-rotate-reverse {
  0% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
}

@keyframes text-glow {
  0% {
    text-shadow: 0 0 20px currentColor, 0 0 40px currentColor;
  }
  100% {
    text-shadow: 0 0 30px currentColor, 0 0 60px currentColor,
      0 0 80px currentColor;
  }
}

@keyframes ring-rotate-slow {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 保持原有的上下浮动动画，但增强效果
@keyframes shake-y {
  0% {
    transform: translate(0px, 0px);
  }
  25% {
    transform: translate(0, 8px) scale(1.02);
  }
  50% {
    transform: translate(0, 15px) scale(1.05);
  }
  75% {
    transform: translate(0, 8px) scale(1.02);
  }
  100% {
    transform: translate(0px, 0px);
  }
}

// ========== 新增增强动画关键帧 ==========

// 增强版背景光效动画
@keyframes glow-pulse-enhanced {
  0% {
    transform: translate(-50%, -50%) scale(0.95);
    opacity: 0.4;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 0.6;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.15);
    opacity: 0.3;
  }
}

// 背景光效旋转动画
@keyframes glow-rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 增强版盾牌浮动动画
@keyframes shield-float-enhanced {
  0% {
    transform: translateY(0px) scale(1);
  }
  25% {
    transform: translateY(-8px) scale(1.01);
  }
  50% {
    transform: translateY(-15px) scale(1.02);
  }
  75% {
    transform: translateY(-8px) scale(1.01);
  }
  100% {
    transform: translateY(0px) scale(1);
  }
}

// 盾牌呼吸动画
@keyframes shield-breathe {
  0% {
    filter: drop-shadow(0 0 25px rgba(68, 226, 254, 0.4))
      drop-shadow(0 0 50px rgba(68, 226, 254, 0.2));
  }
  50% {
    filter: drop-shadow(0 0 35px rgba(68, 226, 254, 0.6))
      drop-shadow(0 0 70px rgba(68, 226, 254, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 25px rgba(68, 226, 254, 0.4))
      drop-shadow(0 0 50px rgba(68, 226, 254, 0.2));
  }
}

// 盾牌微妙旋转动画
@keyframes shield-subtle-rotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(0.5deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(-0.5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

// 盾牌内部光效动画
@keyframes shield-inner-glow {
  0% {
    opacity: 0.12;
    transform: scale(1);
  }
  50% {
    opacity: 0.18;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.12;
    transform: scale(1);
  }
}

// 盾牌内部脉冲动画
@keyframes shield-inner-pulse {
  0% {
    opacity: 0.08;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.15;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.08;
    transform: scale(0.95);
  }
}

// 增强版光环旋转动画
@keyframes ring-rotate-enhanced {
  0% {
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
    opacity: 0.5;
  }
  25% {
    transform: translate(-50%, -50%) rotate(90deg) scale(1.02);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) rotate(180deg) scale(1.05);
    opacity: 0.7;
  }
  75% {
    transform: translate(-50%, -50%) rotate(270deg) scale(1.02);
    opacity: 0.6;
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg) scale(1);
    opacity: 0.5;
  }
}

// 光环脉冲增强动画
@keyframes ring-pulse-enhanced {
  0% {
    box-shadow: inset 0 0 20px currentColor;
    opacity: 0.5;
  }
  50% {
    box-shadow: inset 0 0 40px currentColor, 0 0 20px currentColor;
    opacity: 0.7;
  }
  100% {
    box-shadow: inset 0 0 20px currentColor;
    opacity: 0.5;
  }
}

// 增强版外环旋转动画
@keyframes ring-rotate-reverse-enhanced {
  0% {
    transform: translate(-50%, -50%) rotate(360deg) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translate(-50%, -50%) rotate(270deg) scale(0.98);
    opacity: 0.4;
  }
  50% {
    transform: translate(-50%, -50%) rotate(180deg) scale(0.95);
    opacity: 0.5;
  }
  75% {
    transform: translate(-50%, -50%) rotate(90deg) scale(0.98);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
    opacity: 0.3;
  }
}

// 光环缩放脉冲动画
@keyframes ring-scale-pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.08);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
}

// 增强版文字发光动画
@keyframes text-glow-enhanced {
  0% {
    text-shadow: 0 0 25px currentColor, 0 0 50px currentColor,
      0 0 75px currentColor;
  }
  50% {
    text-shadow: 0 0 35px currentColor, 0 0 70px currentColor,
      0 0 105px currentColor, 0 0 140px rgba(68, 226, 254, 0.3);
  }
  100% {
    text-shadow: 0 0 25px currentColor, 0 0 50px currentColor,
      0 0 75px currentColor;
  }
}

// 文字缩放脉冲动画
@keyframes text-scale-pulse {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.02);
  }
  50% {
    transform: scale(1.05);
  }
  75% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

// 文字背景脉冲动画
@keyframes text-bg-pulse {
  0% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1);
  }
}

// 盾牌光晕呼吸动画 - 与盾牌浮动同步
@keyframes shield-aura-pulse {
  0% {
    opacity: 0.15;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.25;
    transform: translate(-50%, -50%) scale(1.05);
  }
  100% {
    opacity: 0.15;
    transform: translate(-50%, -50%) scale(1);
  }
}

// 盾牌光晕闪烁动画 - 微妙的光影变化
@keyframes shield-aura-shimmer {
  0% {
    opacity: 0.2;
    transform: scale(1) rotate(0deg);
  }
  25% {
    opacity: 0.3;
    transform: scale(1.02) rotate(1deg);
  }
  50% {
    opacity: 0.15;
    transform: scale(1.05) rotate(0deg);
  }
  75% {
    opacity: 0.25;
    transform: scale(1.02) rotate(-1deg);
  }
  100% {
    opacity: 0.2;
    transform: scale(1) rotate(0deg);
  }
}

// 响应式适配 - 中等屏幕
@media (max-width: 1200px) {
  .enhanced-shield-container {
    min-height: 280px;
    flex: 0 0 65%; // 给轮播更多空间
  }

  .enhanced-shield {
    // 在中等屏幕上使用稍小的相对单位，保持修长比例
    width: min(26vw, 300px); // 减少宽度：30vw → 26vw
    height: min(48vw, 512px); // 保持高度，优化宽高比
  }

  // .shield-glow-bg {
  //   width: min(35vw, 360px);
  //   height: min(35vw, 360px);
  // }

  // .shield-ring-enhanced {
  //   width: min(29vw, 350px); // 中等屏幕下椭圆形光晕，与修长盾牌协调
  //   height: min(50vw, 520px); // 高度比例与盾牌一致
  // }

  // .shield-ring-outer-enhanced {
  //   width: min(28vw, 320px);
  //   height: min(28vw, 320px);
  // }

  .shield-inner-decoration {
    width: min(20vw, 220px);
    height: min(20vw, 220px);
  }
}

@media (max-width: 768px) {
  .enhanced-shield-container {
    min-height: 220px;
    flex: 0 0 60%; // 小屏幕给轮播更多空间
  }

  .enhanced-shield {
    // 小屏幕上使用更小的相对单位，保持修长比例
    width: min(38vw, 220px); // 减少宽度：45vw → 38vw
    height: min(72vw, 400px); // 保持高度，确保小屏幕显示效果
  }

  .enhanced-posture-text {
    font-size: 48px;
  }

  .enhanced-posture-label {
    font-size: 18px;
  }
}

// 大屏幕优化 - 在大屏上适当放大以保持视觉效果
@media (min-width: 1920px) {
  .enhanced-shield-container {
    min-height: 400px;
  }

  .enhanced-shield {
    // 大屏幕上使用更大的视窗比例，确保显示效果，保持修长比例
    width: min(24vw, 420px); // 减少宽度：28vw → 24vw
    height: min(45vw, 768px); // 保持高度，优化大屏显示
  }

  // .shield-glow-bg {
  //   width: min(32vw, 520px);
  //   height: min(32vw, 520px);
  // }

  .energy-pulse-container {
    width: min(32vw, 520px);
    height: min(32vw, 520px);
  }

  // .shield-ring-enhanced {
  //   width: min(27vw, 420px); // 大屏幕下椭圆形光晕，与修长盾牌协调
  //   height: min(48vw, 720px); // 高度比例与盾牌一致
  // }

  // .shield-ring-outer-enhanced {
  //   width: min(27vw, 420px);
  //   height: min(27vw, 420px);
  // }

  .shield-inner-decoration {
    width: min(18vw, 280px);
    height: min(18vw, 280px);
  }

  .enhanced-posture-text {
    font-size: 72px; // 大屏幕上增大字体
  }

  .enhanced-posture-label {
    font-size: 32px;
  }
}

// 超大屏幕优化 (4K及以上)
@media (min-width: 2560px) {
  .enhanced-shield-container {
    flex: 0 0 60%; // 4K屏幕给轮播更多空间
  }

  .enhanced-shield {
    width: min(18vw, 520px); // 4K屏幕上进一步优化，减少宽度保持修长
    height: min(35vw, 960px); // 保持高度，确保4K屏幕良好显示
  }

  // 修复4K屏幕下背景光效自适应问题
  // .shield-glow-bg {
  //   width: min(35vw, 700px); // 4K屏幕下增大背景光效
  //   height: min(35vw, 700px);
  // }

  .energy-pulse-container {
    width: min(35vw, 700px); // 4K屏幕下增大能量脉冲容器
    height: min(35vw, 700px);
  }

  // .shield-ring-enhanced {
  //   width: min(25vw, 560px); // 4K屏幕下椭圆形光晕，与修长盾牌协调
  //   height: min(38vw, 960px); // 高度比例与盾牌一致
  // }

  // .shield-ring-outer-enhanced {
  //   width: min(30vw, 600px); // 4K屏幕下增大外环
  //   height: min(30vw, 600px);
  // }

  .shield-inner-decoration {
    width: min(20vw, 400px); // 4K屏幕下增大内部装饰环
    height: min(20vw, 400px);
  }

  .enhanced-posture-text {
    font-size: 96px;
  }

  .enhanced-posture-label {
    font-size: 40px;
  }
}
</style>
